import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Configuration Supabase - remplacez par vos vraies valeurs
const supabaseUrl = 'YOUR_SUPABASE_URL';
const supabaseKey = 'YOUR_SUPABASE_SERVICE_ROLE_KEY'; // Utilisez la clé service role

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Parse un fichier CSV et retourne un tableau d'objets
 * Gère les virgules dans les valeurs et les guillemets
 */
function parseCSV(csvContent) {
  const lines = csvContent.trim().split('\n');
  const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
  
  const data = [];
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue; // Ignorer les lignes vides
    
    // Parser la ligne en gérant les guillemets et virgules
    const values = parseCSVLine(line);
    const row = {};
    
    headers.forEach((header, index) => {
      const value = values[index] ? values[index].trim().replace(/"/g, '') : '';
      row[header] = value || null;
    });
    
    data.push(row);
  }
  
  return data;
}

/**
 * Parse une ligne CSV en gérant les guillemets et virgules
 */
function parseCSVLine(line) {
  const values = [];
  let current = '';
  let inQuotes = false;
  
  for (let i = 0; i < line.length; i++) {
    const char = line[i];
    
    if (char === '"') {
      inQuotes = !inQuotes;
    } else if (char === ',' && !inQuotes) {
      values.push(current);
      current = '';
    } else {
      current += char;
    }
  }
  
  values.push(current); // Ajouter la dernière valeur
  return values;
}

/**
 * Valide et convertit une coordonnée GPS
 */
function parseCoordinate(value) {
  if (!value || value === '' || value === 'null' || value === 'NULL') {
    return null;
  }
  
  // Nettoyer la valeur
  const cleaned = value.toString().trim().replace(/[^\d.-]/g, '');
  
  if (cleaned === '' || cleaned === '-') {
    return null;
  }
  
  const parsed = parseFloat(cleaned);
  
  // Vérifier que c'est un nombre valide
  if (isNaN(parsed)) {
    return null;
  }
  
  return parsed;
}

/**
 * Valide les coordonnées GPS
 */
function validateCoordinates(latitude, longitude) {
  if (latitude === null || longitude === null) {
    return false;
  }
  
  // Vérifier les plages valides
  if (latitude < -90 || latitude > 90) {
    return false;
  }
  
  if (longitude < -180 || longitude > 180) {
    return false;
  }
  
  return true;
}

/**
 * Détermine le type de plan d'eau basé sur le nom ou la commune
 */
function determineWaterBodyType(nom, commune) {
  const nomLower = nom.toLowerCase();
  const communeLower = commune ? commune.toLowerCase() : '';
  
  if (nomLower.includes('mer') || nomLower.includes('océan') || nomLower.includes('manche')) {
    return 'mer';
  }
  
  if (nomLower.includes('étang') || nomLower.includes('etang')) {
    return 'etang';
  }
  
  if (nomLower.includes('lac')) {
    return 'lac';
  }
  
  if (nomLower.includes('rivière') || nomLower.includes('riviere') || 
      nomLower.includes('fleuve') || nomLower.includes('canal')) {
    return 'riviere';
  }
  
  // Par défaut, considérer comme un lac
  return 'lac';
}

/**
 * Nettoie et valide le nom d'une commune
 */
function cleanCommuneName(commune) {
  if (!commune || typeof commune !== 'string') {
    return null;
  }
  
  // Nettoyer le nom de la commune
  const cleaned = commune
    .trim()
    .replace(/[""'']/g, '') // Supprimer les guillemets
    .replace(/\s+/g, ' ')   // Normaliser les espaces
    .toUpperCase();         // Uniformiser en majuscules
  
  // Vérifier que le nom n'est pas vide après nettoyage
  if (cleaned === '' || cleaned === 'NULL' || cleaned === 'UNDEFINED') {
    return null;
  }
  
  return cleaned;
}

/**
 * Génère un nom unique pour le plan d'eau
 */
function generatePlanEauName(commune, ecopage, index = 0) {
  // S'assurer que la commune est valide
  if (!commune || commune.trim() === '') {
    return `Plan d'eau inconnu ${Date.now()}${index > 0 ? `-${index}` : ''}`;
  }
  
  const cleanCommune = commune.trim();
  const suffix = ecopage ? '(Écopage)' : '(Non écopage)';
  const indexSuffix = index > 0 ? ` #${index + 1}` : '';
  
  return `Plan d'eau ${cleanCommune} ${suffix}${indexSuffix}`;
}

/**
 * Convertit les données CSV en format compatible avec la base
 */
function convertCSVToPlansEau(csvData) {
  const validPlansEau = [];
  const invalidEntries = [];
  const communeCounter = new Map(); // Pour éviter les doublons de noms
  
  csvData.forEach((row, index) => {
    const lineNumber = index + 2; // +2 car index commence à 0 et on ignore l'en-tête
    
    // Nettoyer et valider le nom de la commune
    const communeRaw = row.Commune || row.commune || row.COMMUNE;
    const commune = cleanCommuneName(communeRaw);
    
    // Vérifier que la commune existe et est valide
    if (!commune) {
      invalidEntries.push({
        line: lineNumber,
        reason: `Commune manquante ou invalide: "${communeRaw}"`,
        data: row
      });
      return;
    }
    
    // Parser les coordonnées
    const latitude = parseCoordinate(row.Lat || row.lat || row.LAT);
    const longitude = parseCoordinate(row.Long || row.long || row.LONG || row.Lon || row.lon || row.LON);
    
    // Vérifier si les coordonnées sont valides
    if (!validateCoordinates(latitude, longitude)) {
      invalidEntries.push({
        line: lineNumber,
        reason: `Coordonnées invalides (Lat: ${row.Lat || 'vide'}, Long: ${row.Long || 'vide'})`,
        data: row
      });
      return;
    }
    
    // Déterminer si l'écopage est possible
    const accesRaw = row.Accès || row.acces || row.ACCES || row.Access || row.access || '';
    const ecopage = accesRaw.toLowerCase().includes('ecopage');
    
    // Générer un nom unique pour éviter les doublons
    const communeKey = commune.toLowerCase();
    const currentCount = communeCounter.get(communeKey) || 0;
    communeCounter.set(communeKey, currentCount + 1);
    
    const nom = generatePlanEauName(commune, ecopage, currentCount);
    
    // Déterminer le type de plan d'eau
    const type = determineWaterBodyType(nom, commune);
    
    // Créer l'objet plan d'eau
    const planEau = {
      nom: nom, // S'assurer que le nom n'est jamais null
      commune: commune,
      latitude: latitude,
      longitude: longitude,
      type: type,
      accessible: true, // Accessible si on a les coordonnées
      ecopage: ecopage
    };
    
    // Validation finale avant ajout
    if (!planEau.nom || planEau.nom.trim() === '') {
      invalidEntries.push({
        line: lineNumber,
        reason: 'Impossible de générer un nom valide pour le plan d\'eau',
        data: row
      });
      return;
    }
    
    validPlansEau.push(planEau);
  });
  
  return { validPlansEau, invalidEntries };
}

/**
 * Importe les plans d'eau depuis un fichier CSV
 */
async function importPlansEauFromCSV(csvFilePath) {
  try {
    console.log('🚀 Début de l\'import des plans d\'eau...\n');
    
    // Vérifier que le fichier existe
    if (!fs.existsSync(csvFilePath)) {
      throw new Error(`Le fichier ${csvFilePath} n'existe pas`);
    }
    
    // Lire le fichier CSV
    const csvContent = fs.readFileSync(csvFilePath, 'utf-8');
    console.log('✅ Fichier CSV lu avec succès');
    
    // Détecter l'encodage et nettoyer le contenu
    const cleanContent = csvContent
      .replace(/^\uFEFF/, '') // Supprimer BOM UTF-8
      .replace(/\r\n/g, '\n') // Normaliser les fins de ligne
      .replace(/\r/g, '\n');
    
    // Parser le CSV
    const csvData = parseCSV(cleanContent);
    console.log(`📊 ${csvData.length} lignes trouvées dans le CSV`);
    
    if (csvData.length === 0) {
      throw new Error('Aucune donnée trouvée dans le fichier CSV');
    }
    
    // Afficher un aperçu des en-têtes
    const headers = Object.keys(csvData[0]);
    console.log(`📋 En-têtes détectés: ${headers.join(', ')}`);
    
    // Vérifier les en-têtes requis (flexible)
    const requiredHeaders = ['Commune', 'Accès', 'Lat', 'Long'];
    const headerVariants = {
      'Commune': ['Commune', 'commune', 'COMMUNE'],
      'Accès': ['Accès', 'acces', 'ACCES', 'Access', 'access'],
      'Lat': ['Lat', 'lat', 'LAT', 'Latitude', 'latitude'],
      'Long': ['Long', 'long', 'LONG', 'Lon', 'lon', 'LON', 'Longitude', 'longitude']
    };
    
    const missingHeaders = [];
    for (const required of requiredHeaders) {
      const variants = headerVariants[required];
      const found = variants.some(variant => headers.includes(variant));
      if (!found) {
        missingHeaders.push(`${required} (ou variantes: ${variants.join(', ')})`);
      }
    }
    
    if (missingHeaders.length > 0) {
      throw new Error(`En-têtes manquants: ${missingHeaders.join(', ')}`);
    }
    
    // Convertir en format plans d'eau
    const { validPlansEau, invalidEntries } = convertCSVToPlansEau(csvData);
    
    console.log(`🔄 ${validPlansEau.length} plans d'eau valides à importer`);
    
    if (invalidEntries.length > 0) {
      console.log(`⚠️ ${invalidEntries.length} entrées invalides ignorées:`);
      invalidEntries.slice(0, 5).forEach(entry => {
        console.log(`  • Ligne ${entry.line}: ${entry.reason}`);
      });
      if (invalidEntries.length > 5) {
        console.log(`  ... et ${invalidEntries.length - 5} autres erreurs`);
      }
    }
    
    if (validPlansEau.length === 0) {
      console.log('❌ Aucun plan d\'eau valide trouvé');
      return;
    }
    
    // Afficher un aperçu des données valides
    console.log('\n📋 Aperçu des données à importer:');
    validPlansEau.slice(0, 3).forEach((planEau, index) => {
      console.log(`${index + 1}. ${planEau.nom}`);
      console.log(`   Commune: ${planEau.commune}`);
      console.log(`   Coordonnées: ${planEau.latitude}, ${planEau.longitude}`);
      console.log(`   Type: ${planEau.type}, Écopage: ${planEau.ecopage ? 'Oui' : 'Non'}`);
    });
    if (validPlansEau.length > 3) {
      console.log(`... et ${validPlansEau.length - 3} autres`);
    }
    
    console.log('\n⚠️ Cette opération va ajouter ces plans d\'eau à la base de données.');
    console.log('Assurez-vous que vos clés Supabase sont correctement configurées.\n');
    
    // Validation finale avant insertion
    const invalidNames = validPlansEau.filter(p => !p.nom || p.nom.trim() === '');
    if (invalidNames.length > 0) {
      throw new Error(`${invalidNames.length} plans d'eau ont des noms invalides`);
    }
    
    // Insérer en base par petits lots pour éviter les timeouts
    const batchSize = 50;
    let totalInserted = 0;
    
    for (let i = 0; i < validPlansEau.length; i += batchSize) {
      const batch = validPlansEau.slice(i, i + batchSize);
      
      console.log(`📤 Import du lot ${Math.floor(i/batchSize) + 1}/${Math.ceil(validPlansEau.length/batchSize)} (${batch.length} éléments)...`);
      
      // Validation supplémentaire du lot
      batch.forEach((planEau, batchIndex) => {
        if (!planEau.nom || planEau.nom.trim() === '') {
          throw new Error(`Plan d'eau ${i + batchIndex + 1} a un nom invalide: "${planEau.nom}"`);
        }
      });
      
      const { data, error } = await supabase
        .from('plans_eau')
        .insert(batch);
      
      if (error) {
        console.error(`❌ Erreur lors de l'import du lot ${Math.floor(i/batchSize) + 1}:`, error.message);
        console.error('Détails de l\'erreur:', error);
        
        // Afficher le contenu du lot problématique
        console.log('📋 Contenu du lot problématique:');
        batch.forEach((item, idx) => {
          console.log(`  ${idx + 1}. Nom: "${item.nom}" | Commune: "${item.commune}" | Coords: ${item.latitude}, ${item.longitude}`);
        });
        
        throw error;
      }
      
      totalInserted += batch.length;
      console.log(`✅ Lot ${Math.floor(i/batchSize) + 1} importé avec succès`);
    }
    
    console.log(`\n🎉 ${totalInserted} plans d'eau importés avec succès !`);
    
    // Statistiques finales
    const ecopageCount = validPlansEau.filter(p => p.ecopage).length;
    const nonEcopageCount = validPlansEau.filter(p => !p.ecopage).length;
    const typeStats = validPlansEau.reduce((acc, p) => {
      acc[p.type] = (acc[p.type] || 0) + 1;
      return acc;
    }, {});
    
    console.log('\n📈 Statistiques finales:');
    console.log(`• Plans d'eau avec écopage: ${ecopageCount}`);
    console.log(`• Plans d'eau sans écopage: ${nonEcopageCount}`);
    console.log(`• Répartition par type:`);
    Object.entries(typeStats).forEach(([type, count]) => {
      console.log(`  - ${type}: ${count}`);
    });
    console.log(`• Total importé: ${totalInserted}`);
    console.log(`• Entrées ignorées: ${invalidEntries.length}`);
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'import:', error.message);
    
    // Afficher des conseils de dépannage
    console.log('\n🔧 Conseils de dépannage:');
    console.log('• Vérifiez que le fichier CSV est bien formaté');
    console.log('• Assurez-vous que les colonnes Commune, Accès, Lat, Long existent');
    console.log('• Vérifiez que vos clés Supabase sont correctes');
    console.log('• Essayez avec un fichier CSV plus petit pour tester');
    console.log('• Vérifiez qu\'aucune commune n\'est vide ou null');
    
    process.exit(1);
  }
}

/**
 * Nettoie tous les plans d'eau existants
 */
async function clearPlansEau() {
  try {
    console.log('🧹 Nettoyage des plans d\'eau existants...');
    
    const { error } = await supabase
      .from('plans_eau')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Supprimer tout
    
    if (error) {
      throw error;
    }
    
    console.log('✅ Plans d\'eau existants supprimés');
  } catch (error) {
    console.error('❌ Erreur lors du nettoyage:', error.message);
    throw error;
  }
}

/**
 * Valide un fichier CSV avant import
 */
async function validateCSV(csvFilePath) {
  try {
    console.log('🔍 Validation du fichier CSV...\n');
    
    if (!fs.existsSync(csvFilePath)) {
      throw new Error(`Le fichier ${csvFilePath} n'existe pas`);
    }
    
    const csvContent = fs.readFileSync(csvFilePath, 'utf-8');
    const cleanContent = csvContent
      .replace(/^\uFEFF/, '')
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n');
    
    const csvData = parseCSV(cleanContent);
    
    if (csvData.length === 0) {
      throw new Error('Aucune donnée trouvée');
    }
    
    const headers = Object.keys(csvData[0]);
    console.log(`📋 En-têtes détectés: ${headers.join(', ')}`);
    
    // Vérification flexible des en-têtes
    const headerVariants = {
      'Commune': ['Commune', 'commune', 'COMMUNE'],
      'Accès': ['Accès', 'acces', 'ACCES', 'Access', 'access'],
      'Lat': ['Lat', 'lat', 'LAT', 'Latitude', 'latitude'],
      'Long': ['Long', 'long', 'LONG', 'Lon', 'lon', 'LON', 'Longitude', 'longitude']
    };
    
    const missingHeaders = [];
    for (const [required, variants] of Object.entries(headerVariants)) {
      const found = variants.some(variant => headers.includes(variant));
      if (!found) {
        missingHeaders.push(`${required} (variantes: ${variants.join(', ')})`);
      }
    }
    
    if (missingHeaders.length > 0) {
      throw new Error(`En-têtes manquants: ${missingHeaders.join(', ')}`);
    }
    
    const { validPlansEau, invalidEntries } = convertCSVToPlansEau(csvData);
    
    console.log('✅ Validation réussie');
    console.log(`📊 ${csvData.length} lignes analysées`);
    console.log(`✅ ${validPlansEau.length} entrées valides`);
    console.log(`⚠️ ${invalidEntries.length} entrées invalides`);
    
    if (invalidEntries.length > 0) {
      console.log('\n❌ Erreurs détectées:');
      invalidEntries.forEach(entry => {
        console.log(`  • Ligne ${entry.line}: ${entry.reason}`);
      });
    }
    
    // Vérifier les noms générés
    const invalidNames = validPlansEau.filter(p => !p.nom || p.nom.trim() === '');
    if (invalidNames.length > 0) {
      console.log(`\n⚠️ ${invalidNames.length} plans d'eau ont des noms invalides`);
      return { valid: false, error: 'Noms de plans d\'eau invalides détectés' };
    }
    
    // Afficher un aperçu des noms générés
    console.log('\n📋 Aperçu des noms générés:');
    validPlansEau.slice(0, 5).forEach((planEau, index) => {
      console.log(`  ${index + 1}. "${planEau.nom}" (${planEau.commune})`);
    });
    if (validPlansEau.length > 5) {
      console.log(`  ... et ${validPlansEau.length - 5} autres`);
    }
    
    return { valid: validPlansEau.length > 0, validCount: validPlansEau.length, invalidCount: invalidEntries.length };
    
  } catch (error) {
    console.error('❌ Erreur de validation:', error.message);
    return { valid: false, error: error.message };
  }
}

// Script principal
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Usage: node importPlansEau.js <chemin_vers_fichier.csv> [options]');
    console.log('');
    console.log('Options:');
    console.log('  --clear     Supprime tous les plans d\'eau existants avant l\'import');
    console.log('  --validate  Valide seulement le fichier sans importer');
    console.log('  --help      Affiche cette aide');
    console.log('');
    console.log('Exemples:');
    console.log('  node importPlansEau.js ./data/plans_eau.csv --validate');
    console.log('  node importPlansEau.js ./data/plans_eau.csv --clear');
    console.log('  node importPlansEau.js ./data/plans_eau.csv');
    process.exit(1);
  }
  
  const csvFilePath = args[0];
  const shouldClear = args.includes('--clear');
  const validateOnly = args.includes('--validate');
  
  if (validateOnly) {
    await validateCSV(csvFilePath);
    return;
  }
  
  if (shouldClear) {
    await clearPlansEau();
  }
  
  await importPlansEauFromCSV(csvFilePath);
}

// Exécuter le script si appelé directement
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { importPlansEauFromCSV, clearPlansEau, validateCSV };