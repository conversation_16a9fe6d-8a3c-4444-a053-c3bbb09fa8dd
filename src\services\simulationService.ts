import { supabase } from '../lib/supabase';
import { Simulation, SaveSimulationData } from '../types';

/**
 * Service pour gérer les simulations sauvegardées
 */

/**
 * Sauvegarde une nouvelle simulation
 */
export async function saveSimulation(simulationData: SaveSimulationData): Promise<Simulation> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('Utilisateur non authentifié');
    }

    const { data, error } = await supabase
      .from('simulations')
      .insert({
        user_id: user.id,
        nom: simulationData.nom,
        description: simulationData.description,
        commune_data: simulationData.commune_data,
        intervention_params: simulationData.intervention_params,
        simulator_settings: simulationData.simulator_settings,
        results: simulationData.results
      })
      .select()
      .single();

    if (error) {
      console.error('Erreur lors de la sauvegarde de la simulation:', error);
      if (error.code === 'PGRST106') {
        throw new Error('La table des simulations n\'existe pas encore. Veuillez créer la table dans Supabase.');
      }
      throw error;
    }

    return {
      id: data.id,
      user_id: data.user_id,
      nom: data.nom,
      description: data.description,
      commune_data: data.commune_data,
      intervention_params: data.intervention_params,
      simulator_settings: data.simulator_settings,
      results: data.results,
      created_at: data.created_at,
      updated_at: data.updated_at
    };
  } catch (error) {
    console.error('Erreur lors de la sauvegarde de la simulation:', error);
    throw error;
  }
}

/**
 * Récupère toutes les simulations de l'utilisateur connecté
 */
export async function getUserSimulations(): Promise<Simulation[]> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('Utilisateur non authentifié');
    }

    const { data, error } = await supabase
      .from('simulations')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erreur lors de la récupération des simulations:', error);
      throw error;
    }

    return data.map(simulation => ({
      id: simulation.id,
      user_id: simulation.user_id,
      nom: simulation.nom,
      description: simulation.description,
      commune_data: simulation.commune_data,
      intervention_params: simulation.intervention_params,
      simulator_settings: simulation.simulator_settings,
      results: simulation.results,
      created_at: simulation.created_at,
      updated_at: simulation.updated_at
    }));
  } catch (error) {
    console.error('Erreur lors de la récupération des simulations:', error);
    return [];
  }
}

/**
 * Récupère une simulation spécifique par son ID
 */
export async function getSimulation(id: string): Promise<Simulation | null> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('Utilisateur non authentifié');
    }

    const { data, error } = await supabase
      .from('simulations')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Simulation non trouvée
      }
      console.error('Erreur lors de la récupération de la simulation:', error);
      throw error;
    }

    return {
      id: data.id,
      user_id: data.user_id,
      nom: data.nom,
      description: data.description,
      commune_data: data.commune_data,
      intervention_params: data.intervention_params,
      simulator_settings: data.simulator_settings,
      results: data.results,
      created_at: data.created_at,
      updated_at: data.updated_at
    };
  } catch (error) {
    console.error('Erreur lors de la récupération de la simulation:', error);
    throw error;
  }
}

/**
 * Met à jour une simulation existante
 */
export async function updateSimulation(id: string, updates: Partial<SaveSimulationData>): Promise<Simulation> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('Utilisateur non authentifié');
    }

    const { data, error } = await supabase
      .from('simulations')
      .update(updates)
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single();

    if (error) {
      console.error('Erreur lors de la mise à jour de la simulation:', error);
      throw error;
    }

    return {
      id: data.id,
      user_id: data.user_id,
      nom: data.nom,
      description: data.description,
      commune_data: data.commune_data,
      intervention_params: data.intervention_params,
      simulator_settings: data.simulator_settings,
      results: data.results,
      created_at: data.created_at,
      updated_at: data.updated_at
    };
  } catch (error) {
    console.error('Erreur lors de la mise à jour de la simulation:', error);
    throw error;
  }
}

/**
 * Supprime une simulation
 */
export async function deleteSimulation(id: string): Promise<void> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('Utilisateur non authentifié');
    }

    const { error } = await supabase
      .from('simulations')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id);

    if (error) {
      console.error('Erreur lors de la suppression de la simulation:', error);
      throw error;
    }
  } catch (error) {
    console.error('Erreur lors de la suppression de la simulation:', error);
    throw error;
  }
}
