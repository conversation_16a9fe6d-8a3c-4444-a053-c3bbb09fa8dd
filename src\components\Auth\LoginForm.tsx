import React, { useState } from 'react';
import { Mail, Lock, Flame, Info } from 'lucide-react';
import { getFormattedVersion, getBuildInfo } from '../../config/version';

interface LoginFormProps {
  onLogin: (email: string, password: string) => Promise<void>;
  loading?: boolean;
  error?: string;
}

export function LoginForm({ onLogin, loading = false, error }: LoginFormProps) {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('demo123');
  const [showVersionInfo, setShowVersionInfo] = useState(false);

  const buildInfo = getBuildInfo();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onLogin(email, password);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center px-4">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="flex justify-center">
            <div className="bg-red-600 p-4 rounded-full">
              <Flame className="h-12 w-12 text-white" />
            </div>
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">FireSplash</h2>
          <p className="mt-2 text-sm text-gray-600">
            Système d'estimation de moyens aériens
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="bg-white rounded-lg shadow-md p-6 space-y-4">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {error}
              </div>
            )}

            <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded">
              <p className="text-sm">
                <strong>Compte de démonstration :</strong><br/>
                Email: <EMAIL><br/>
                Mot de passe: demo123
              </p>
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Adresse email
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  id="email"
                  type="email"
                  required
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                Mot de passe
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  id="password"
                  type="password"
                  required
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Connexion...' : 'Se connecter'}
            </button>
          </div>
        </form>

        {/* Affichage de la version */}
        <div className="mt-8 text-center">
          <div className="flex items-center justify-center space-x-2">
            <button
              onClick={() => setShowVersionInfo(!showVersionInfo)}
              className="flex items-center space-x-1 text-xs text-gray-500 hover:text-gray-700 transition-colors"
            >
              <Info className="h-3 w-3" />
              <span>{getFormattedVersion()}</span>
            </button>
          </div>

          {showVersionInfo && (
            <div className="mt-3 bg-white rounded-lg shadow-md p-4 text-left">
              <h4 className="font-semibold text-gray-800 mb-2">Informations de version</h4>
              <div className="space-y-1 text-xs text-gray-600">
                <div><strong>Version :</strong> {buildInfo.version}</div>
                <div><strong>Build :</strong> {buildInfo.buildDate}</div>
                <div><strong>Environnement :</strong> {buildInfo.environment}</div>
              </div>

              <div className="mt-3 pt-3 border-t border-gray-200">
                <h5 className="font-medium text-gray-700 mb-2">Fonctionnalités v1.2.0</h5>
                <ul className="text-xs text-gray-600 space-y-1">
                  <li>• Cartographie avancée avec zones de risque</li>
                  <li>• Données météorologiques en temps réel</li>
                  <li>• Sauvegarde des simulations</li>
                  <li>• Paramètres personnalisables</li>
                  <li>• Interface responsive</li>
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}