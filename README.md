# FireSplash - Estimation de moyens aériens

Application MVP pour estimer les besoins en moyens aériens (Canadair/Dash) lors d'interventions incendie.

## Fonctionnalités MVP

- ✅ Authentification utilisateur
- ✅ Recherche de commune avec auto-complétion  
- ✅ Calculs automatiques des moyens nécessaires
- ✅ Visualisation cartographique avec OpenStreetMap
- ✅ Affichage détaillé des résultats
- ✅ Interface responsive

## Technologies

- React 18 + TypeScript
- Tailwind CSS
- Leaflet (OpenStreetMap)
- Supabase (authentification et base de données)
- Lucide React (icônes)

## Installation et démarrage

1. Cloner le projet
2. Installer les dépendances : `npm install`
3. Configurer les variables d'environnement (voir `.env.example`)
4. Démarrer le serveur de développement : `npm run dev`

## Authentification de démonstration

Pour tester l'application :
- Email: `<EMAIL>`
- Mot de passe: `demo123`

## Structure du projet

```
src/
├── components/
│   ├── Auth/           # Composants d'authentification
│   ├── Dashboard/      # Interface principale
│   └── Layout/         # Composants de mise en page
├── data/              # Données de référence (communes, bases, plans d'eau)
├── hooks/             # Hooks React personnalisés
├── lib/               # Configuration des services externes
├── services/          # Logique métier et calculs
└── types/             # Définitions TypeScript
```

## Calculs automatiques

L'application effectue automatiquement :
- Recherche du plan d'eau le plus proche
- Recherche de la base Dash la plus proche
- Calcul des performances par appareil (largages/heure)
- Estimation du nombre d'appareils nécessaires
- Arrondi à l'unité supérieure pour les recommandations

## Évolutions prévues (V2+)

- Gestion de multiples bases d'appareils
- Prise en compte de la disponibilité en temps réel
- Stockage des simulations
- Interface d'administration
- Intégration de cartes de végétation/risque