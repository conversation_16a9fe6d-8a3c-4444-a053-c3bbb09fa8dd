import { Commune, PlanEau, BaseDash, CalculResult, InterventionParams, SimulatorSettings } from '../types';
import { getPlansEau, getBasesDash } from './supabaseService';

/**
 * Calcule la distance entre deux points GPS (formule de Haversine)
 */
function calculerDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371; // Rayon de la Terre en km
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

/**
 * Trouve le plan d'eau le plus proche d'une commune
 */
async function trouverPlanEauProche(commune: Commune): Promise<{ planEau: PlanEau; distance: number }> {
  const plansEau = await getPlansEau();
  
  if (plansEau.length === 0) {
    throw new Error('Aucun plan d\'eau disponible');
  }

  let planEauProche = plansEau[0];
  let distanceMin = calculerDistance(
    commune.latitude, commune.longitude,
    planEauProche.latitude, planEauProche.longitude
  );

  plansEau.forEach(planEau => {
    if (planEau.accessible) {
      const distance = calculerDistance(
        commune.latitude, commune.longitude,
        planEau.latitude, planEau.longitude
      );
      if (distance < distanceMin) {
        distanceMin = distance;
        planEauProche = planEau;
      }
    }
  });

  return { planEau: planEauProche, distance: distanceMin };
}

/**
 * Trouve la base Dash la plus proche d'une commune
 */
async function trouverBaseDashProche(commune: Commune): Promise<{ baseDash: BaseDash; distance: number }> {
  const basesDash = await getBasesDash();
  
  if (basesDash.length === 0) {
    throw new Error('Aucune base Dash disponible');
  }

  let baseDashProche = basesDash[0];
  let distanceMin = calculerDistance(
    commune.latitude, commune.longitude,
    baseDashProche.latitude, baseDashProche.longitude
  );

  basesDash.forEach(baseDash => {
    if (baseDash.disponible) {
      const distance = calculerDistance(
        commune.latitude, commune.longitude,
        baseDash.latitude, baseDash.longitude
      );
      if (distance < distanceMin) {
        distanceMin = distance;
        baseDashProche = baseDash;
      }
    }
  });

  return { baseDash: baseDashProche, distance: distanceMin };
}

/**
 * Calcule le nombre de largages qu'un appareil peut effectuer en une heure
 * selon l'algorithme fourni
 */
function calculerLargagesParHeure(
  distance: number, 
  vitesse: number, 
  tempsChargement: number,
  dureeTotaleMission: number
): number {
  // Conversion de la vitesse en km/min
  const vitesseKmMin = vitesse / 60;
  
  // Distance aller-retour
  const distanceAllerRetour = distance * 2;
  
  // Temps de vol aller-retour en minutes
  const tempsVolMin = distanceAllerRetour / vitesseKmMin;
  
  // Temps total d'un cycle (vol + chargement)
  const tempsCycleMin = tempsVolMin + tempsChargement;
  
  // Nombre de cycles complets possibles dans la durée de mission
  const cyclesParAppareil = Math.floor(dureeTotaleMission / tempsCycleMin);
  
  // Nombre de largages = 1 initial + cycles complets
  const largagesParAppareil = 1 + cyclesParAppareil;
  
  return largagesParAppareil;
}

/**
 * Calcule le nombre d'appareils nécessaires selon l'algorithme fourni
 * Retourne le calcul exact (avec décimales)
 */
function calculerNombreAppareilsExact(objectifLargages: number, largagesParAppareil: number): number {
  return objectifLargages / largagesParAppareil;
}

/**
 * Calcule le nombre d'appareils nécessaires selon l'algorithme fourni
 * Retourne le nombre entier selon la règle d'arrondi spécifiée
 */
function calculerNombreAppareils(objectifLargages: number, largagesParAppareil: number): number {
  if (objectifLargages % largagesParAppareil === 0) {
    return objectifLargages / largagesParAppareil;
  } else {
    return Math.floor(objectifLargages / largagesParAppareil) + 1;
  }
}

/**
 * Effectue tous les calculs pour une intervention en utilisant les paramètres de la base
 */
export async function calculerMoyensAeriens(
  commune: Commune, 
  params: InterventionParams,
  settings: SimulatorSettings
): Promise<CalculResult> {
  try {
    // Trouver les points de ravitaillement les plus proches
    const { planEau: planEauProche, distance: distanceEau } = await trouverPlanEauProche(commune);
    const { baseDash: baseDashProche, distance: distanceDash } = await trouverBaseDashProche(commune);

    // Appliquer les facteurs environnementaux
    const distanceEauAjustee = distanceEau * settings.facteurVent * (2 - settings.facteurVisibilite);
    const distanceDashAjustee = distanceDash * settings.facteurVent * (2 - settings.facteurVisibilite);

    // Calculer les performances des appareils avec les paramètres utilisateur
    const largagesCanadairParHeure = calculerLargagesParHeure(
      distanceEauAjustee, 
      settings.canadair.vitesseCroisiere, 
      settings.canadair.tempsRemplissage,
      settings.dureeMission
    );
    
    const largagesDashParHeure = calculerLargagesParHeure(
      distanceDashAjustee, 
      settings.dash.vitesseCroisiere, 
      settings.dash.tempsRemplissage,
      settings.dureeMission
    );

    // Calculer le nombre d'appareils nécessaires selon l'algorithme
    const canadairsExact = calculerNombreAppareilsExact(params.exigencesCanadair, largagesCanadairParHeure);
    const dashsExact = calculerNombreAppareilsExact(params.exigencesDash, largagesDashParHeure);
    
    const canadairsNecessaires = calculerNombreAppareils(params.exigencesCanadair, largagesCanadairParHeure);
    const dashsNecessaires = calculerNombreAppareils(params.exigencesDash, largagesDashParHeure);

    // Appliquer la marge de sécurité
    const margeSecurite = 1 + (settings.margeSecurite / 100);
    const canadairsAvecMarge = canadairsNecessaires * margeSecurite;
    const dashsAvecMarge = dashsNecessaires * margeSecurite;

    return {
      commune,
      planEauProche,
      baseDashProche,
      distanceEau: Math.round(distanceEau * 100) / 100,
      distanceDash: Math.round(distanceDash * 100) / 100,
      largagesCanadairParHeure,
      largagesDashParHeure,
      canadairsExact: Math.round(canadairsExact * 1000) / 1000, // 3 décimales pour le calcul exact
      dashsExact: Math.round(dashsExact * 1000) / 1000, // 3 décimales pour le calcul exact
      canadairsNecessaires,
      dashsNecessaires,
      canadairsArrondi: Math.ceil(canadairsAvecMarge),
      dashsArrondi: Math.ceil(dashsAvecMarge)
    };
  } catch (error) {
    console.error('Erreur lors du calcul des moyens aériens:', error);
    throw error;
  }
}