/**
 * Configuration de versioning pour FireSplash
 */

// Version principale de l'application
// TODO: Synchroniser avec package.json lors des releases
export const APP_VERSION = '1.2.0';

// Nom de l'application
export const APP_NAME = 'FireSplash';

// Nom complet avec version
export const APP_FULL_NAME = `${APP_NAME} v${APP_VERSION}`;

// Date de build (sera mise à jour automatiquement)
export const BUILD_DATE = new Date().toISOString().split('T')[0];

// Informations détaillées sur la version actuelle
export const VERSION_INFO = {
  version: APP_VERSION,
  name: APP_NAME,
  fullName: APP_FULL_NAME,
  buildDate: BUILD_DATE,
  features: [
    'Estimation automatique des moyens aériens',
    'Cartographie avancée avec zones de risque',
    'Données météorologiques en temps réel',
    'Sauvegarde et gestion des simulations',
    'Paramètres personnalisables',
    'Interface responsive et intuitive'
  ],
  changelog: {
    '1.2.0': [
      'Cartographie avancée avec zones de risque incendie',
      'Données météorologiques intégrées',
      'Couches de relief et végétation',
      'Points de décollage clarifiés',
      'Suppression des trajectoires de vol',
      'Contrôles d\'opacité réactifs'
    ],
    '1.1.0': [
      'Sauvegarde des simulations',
      'Navigation améliorée',
      'Paramètres Canadair (1-40) et Dash (1-10)',
      'Interface utilisateur optimisée'
    ],
    '1.0.0': [
      'Version initiale MVP',
      'Calculs de base des moyens aériens',
      'Authentification utilisateur',
      'Carte simple avec marqueurs'
    ]
  }
};

/**
 * Fonction utilitaire pour obtenir la version formatée
 */
export const getFormattedVersion = (includePrefix = true): string => {
  return includePrefix ? `v${APP_VERSION}` : APP_VERSION;
};

/**
 * Fonction pour obtenir les informations de build
 */
export const getBuildInfo = () => ({
  version: APP_VERSION,
  buildDate: BUILD_DATE,
  environment: import.meta.env.MODE || 'development'
});
