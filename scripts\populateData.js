import { createClient } from '@supabase/supabase-js';

// Configuration Supabase - remplacez par vos vraies valeurs
const supabaseUrl = 'YOUR_SUPABASE_URL';
const supabaseKey = 'YOUR_SUPABASE_SERVICE_ROLE_KEY'; // Utilisez la clé service role pour les insertions en masse

const supabase = createClient(supabaseUrl, supabaseKey);

// Données des communes françaises (échantillon représentatif)
const communesData = [
  // Région Occitanie
  { nom: 'Nîmes', code_postal: '30000', latitude: 43.8367, longitude: 4.3601, departement: 'Gard' },
  { nom: 'Montpellier', code_postal: '34000', latitude: 43.6110, longitude: 3.8767, departement: 'Hérault' },
  { nom: 'Toulouse', code_postal: '31000', latitude: 43.6047, longitude: 1.4442, departement: 'Haute-Garonne' },
  { nom: 'Perpignan', code_postal: '66000', latitude: 42.6886, longitude: 2.8946, departement: 'Pyrénées-Orientales' },
  { nom: 'Béziers', code_postal: '34500', latitude: 43.3415, longitude: 3.2150, departement: 'Hérault' },
  { nom: 'Narbonne', code_postal: '11100', latitude: 43.1839, longitude: 3.0032, departement: 'Aude' },
  { nom: 'Carcassonne', code_postal: '11000', latitude: 43.2130, longitude: 2.3491, departement: 'Aude' },
  { nom: 'Alès', code_postal: '30100', latitude: 44.1250, longitude: 4.0816, departement: 'Gard' },
  { nom: 'Sète', code_postal: '34200', latitude: 43.4030, longitude: 3.6970, departement: 'Hérault' },
  { nom: 'Lunel', code_postal: '34400', latitude: 43.6750, longitude: 4.1360, departement: 'Hérault' },
  
  // Région PACA
  { nom: 'Marseille', code_postal: '13000', latitude: 43.2965, longitude: 5.3698, departement: 'Bouches-du-Rhône' },
  { nom: 'Nice', code_postal: '06000', latitude: 43.7102, longitude: 7.2620, departement: 'Alpes-Maritimes' },
  { nom: 'Toulon', code_postal: '83000', latitude: 43.1242, longitude: 5.9280, departement: 'Var' },
  { nom: 'Aix-en-Provence', code_postal: '13100', latitude: 43.5297, longitude: 5.4474, departement: 'Bouches-du-Rhône' },
  { nom: 'Cannes', code_postal: '06400', latitude: 43.5528, longitude: 7.0174, departement: 'Alpes-Maritimes' },
  { nom: 'Antibes', code_postal: '06600', latitude: 43.5804, longitude: 7.1251, departement: 'Alpes-Maritimes' },
  { nom: 'Avignon', code_postal: '84000', latitude: 43.9493, longitude: 4.8059, departement: 'Vaucluse' },
  { nom: 'Arles', code_postal: '13200', latitude: 43.6768, longitude: 4.6311, departement: 'Bouches-du-Rhône' },
  { nom: 'Fréjus', code_postal: '83600', latitude: 43.4331, longitude: 6.7368, departement: 'Var' },
  { nom: 'Hyères', code_postal: '83400', latitude: 43.1205, longitude: 6.1286, departement: 'Var' },
  
  // Région Nouvelle-Aquitaine
  { nom: 'Bordeaux', code_postal: '33000', latitude: 44.8378, longitude: -0.5792, departement: 'Gironde' },
  { nom: 'Limoges', code_postal: '87000', latitude: 45.8336, longitude: 1.2611, departement: 'Haute-Vienne' },
  { nom: 'Poitiers', code_postal: '86000', latitude: 46.5802, longitude: 0.3404, departement: 'Vienne' },
  { nom: 'Pau', code_postal: '64000', latitude: 43.2951, longitude: -0.3707, departement: 'Pyrénées-Atlantiques' },
  { nom: 'La Rochelle', code_postal: '17000', latitude: 46.1603, longitude: -1.1511, departement: 'Charente-Maritime' },
  { nom: 'Bayonne', code_postal: '64100', latitude: 43.4832, longitude: -1.4748, departement: 'Pyrénées-Atlantiques' },
  { nom: 'Angoulême', code_postal: '16000', latitude: 45.6484, longitude: 0.1561, departement: 'Charente' },
  { nom: 'Périgueux', code_postal: '24000', latitude: 45.1848, longitude: 0.7218, departement: 'Dordogne' },
  { nom: 'Agen', code_postal: '47000', latitude: 44.2028, longitude: 0.6169, departement: 'Lot-et-Garonne' },
  { nom: 'Mont-de-Marsan', code_postal: '40000', latitude: 43.8906, longitude: -0.4999, departement: 'Landes' },
  
  // Autres régions importantes pour les incendies
  { nom: 'Ajaccio', code_postal: '20000', latitude: 41.9196, longitude: 8.7389, departement: 'Corse-du-Sud' },
  { nom: 'Bastia', code_postal: '20200', latitude: 42.7028, longitude: 9.4507, departement: 'Haute-Corse' },
  { nom: 'Porto-Vecchio', code_postal: '20137', latitude: 41.5914, longitude: 9.2806, departement: 'Corse-du-Sud' },
  { nom: 'Calvi', code_postal: '20260', latitude: 42.5679, longitude: 8.7581, departement: 'Haute-Corse' },
  
  // Région Auvergne-Rhône-Alpes (zones à risque)
  { nom: 'Lyon', code_postal: '69000', latitude: 45.7640, longitude: 4.8357, departement: 'Rhône' },
  { nom: 'Grenoble', code_postal: '38000', latitude: 45.1885, longitude: 5.7245, departement: 'Isère' },
  { nom: 'Saint-Étienne', code_postal: '42000', latitude: 45.4397, longitude: 4.3872, departement: 'Loire' },
  { nom: 'Valence', code_postal: '26000', latitude: 44.9334, longitude: 4.8924, departement: 'Drôme' },
  { nom: 'Privas', code_postal: '07000', latitude: 44.7354, longitude: 4.5996, departement: 'Ardèche' },
  
  // Autres communes importantes
  { nom: 'Digne-les-Bains', code_postal: '04000', latitude: 44.0944, longitude: 6.2361, departement: 'Alpes-de-Haute-Provence' },
  { nom: 'Gap', code_postal: '05000', latitude: 44.5598, longitude: 6.0777, departement: 'Hautes-Alpes' },
  { nom: 'Draguignan', code_postal: '83300', latitude: 43.5382, longitude: 6.4678, departement: 'Var' },
  { nom: 'Brignoles', code_postal: '83170', latitude: 43.4044, longitude: 6.0586, departement: 'Var' },
  { nom: 'Orange', code_postal: '84100', latitude: 44.1364, longitude: 4.8089, departement: 'Vaucluse' },
  { nom: 'Carpentras', code_postal: '84200', latitude: 44.0550, longitude: 5.0481, departement: 'Vaucluse' },
  { nom: 'Salon-de-Provence', code_postal: '13300', latitude: 43.6403, longitude: 5.0979, departement: 'Bouches-du-Rhône' },
  { nom: 'Istres', code_postal: '13800', latitude: 43.5135, longitude: 4.9877, departement: 'Bouches-du-Rhône' },
  { nom: 'Martigues', code_postal: '13500', latitude: 43.4074, longitude: 5.0515, departement: 'Bouches-du-Rhône' }
];

// Plans d'eau pour les Canadair (sans colonne type)
const plansEauData = [
  // Mer Méditerranée - points de puisage principaux
  { nom: 'Mer Méditerranée (Palavas-les-Flots)', latitude: 43.5308, longitude: 3.9269, accessible: true, ecopage: true },
  { nom: 'Mer Méditerranée (La Grande-Motte)', latitude: 43.5619, longitude: 4.0828, accessible: true, ecopage: true },
  { nom: 'Mer Méditerranée (Saintes-Maries-de-la-Mer)', latitude: 43.4519, longitude: 4.4281, accessible: true, ecopage: true },
  { nom: 'Mer Méditerranée (Marseille - Prado)', latitude: 43.2584, longitude: 5.3656, accessible: true, ecopage: true },
  { nom: 'Mer Méditerranée (Cassis)', latitude: 43.2148, longitude: 5.5389, accessible: true, ecopage: true },
  { nom: 'Mer Méditerranée (Toulon)', latitude: 43.1047, longitude: 5.9337, accessible: true, ecopage: true },
  { nom: 'Mer Méditerranée (Saint-Tropez)', latitude: 43.2677, longitude: 6.6407, accessible: true, ecopage: true },
  { nom: 'Mer Méditerranée (Cannes)', latitude: 43.5483, longitude: 7.0085, accessible: true, ecopage: true },
  { nom: 'Mer Méditerranée (Nice)', latitude: 43.6951, longitude: 7.2758, accessible: true, ecopage: true },
  { nom: 'Mer Méditerranée (Ajaccio)', latitude: 41.9196, longitude: 8.7389, accessible: true, ecopage: true },
  { nom: 'Mer Méditerranée (Bastia)', latitude: 42.7028, longitude: 9.4507, accessible: true, ecopage: true },
  { nom: 'Mer Méditerranée (Porto-Vecchio)', latitude: 41.5914, longitude: 9.2806, accessible: true, ecopage: true },
  
  // Étangs et lacs importants
  { nom: 'Étang de Berre', latitude: 43.4581, longitude: 5.0892, accessible: true, ecopage: true },
  { nom: 'Étang de Thau', latitude: 43.4167, longitude: 3.6000, accessible: true, ecopage: true },
  { nom: 'Lac de Sainte-Croix', latitude: 43.7750, longitude: 6.2000, accessible: true, ecopage: true },
  { nom: 'Lac de Serre-Ponçon', latitude: 44.5167, longitude: 6.3500, accessible: true, ecopage: true },
  { nom: 'Lac du Bourget', latitude: 45.7167, longitude: 5.8500, accessible: true, ecopage: true },
  { nom: 'Lac Léman (Évian)', latitude: 46.4000, longitude: 6.5833, accessible: true, ecopage: true },
  { nom: 'Lac de Cazaux-Sanguinet', latitude: 44.5167, longitude: -1.1167, accessible: true, ecopage: true },
  { nom: 'Lac de Biscarrosse', latitude: 44.4167, longitude: -1.2333, accessible: true, ecopage: true },
  
  // Océan Atlantique
  { nom: 'Océan Atlantique (Arcachon)', latitude: 44.6667, longitude: -1.1667, accessible: true, ecopage: true },
  { nom: 'Océan Atlantique (Biarritz)', latitude: 43.4832, longitude: -1.5586, accessible: true, ecopage: true },
  { nom: 'Océan Atlantique (La Rochelle)', latitude: 46.1603, longitude: -1.1511, accessible: true, ecopage: true },
  { nom: 'Océan Atlantique (Royan)', latitude: 45.6167, longitude: -1.0333, accessible: true, ecopage: true },
  
  // Rivières importantes (pour certains types d'appareils)
  { nom: 'Rhône (Arles)', latitude: 43.6768, longitude: 4.6311, accessible: true, ecopage: true },
  { nom: 'Garonne (Bordeaux)', latitude: 44.8378, longitude: -0.5792, accessible: true, ecopage: true },
  { nom: 'Loire (Nantes)', latitude: 47.2184, longitude: -1.5536, accessible: true, ecopage: true }
];

// Bases aériennes pour les Dash et autres moyens aériens
const basesDashData = [
  // Bases principales de la Sécurité Civile
  { nom: 'Base Nîmes-Garons', latitude: 43.7567, longitude: 4.4164, capacite: 6, disponible: true },
  { nom: 'Base Marignane', latitude: 43.4355, longitude: 5.2148, capacite: 4, disponible: true },
  { nom: 'Base Perpignan-Rivesaltes', latitude: 42.7397, longitude: 2.8706, capacite: 3, disponible: true },
  { nom: 'Base Ajaccio-Campo dell\'Oro', latitude: 41.9236, longitude: 8.7928, capacite: 2, disponible: true },
  { nom: 'Base Solenzara', latitude: 41.9244, longitude: 9.4061, capacite: 2, disponible: true },
  
  // Bases militaires pouvant être utilisées
  { nom: 'Base Istres-Le Tubé', latitude: 43.5227, longitude: 4.9236, capacite: 4, disponible: true },
  { nom: 'Base Orange-Caritat', latitude: 44.1425, longitude: 4.8678, capacite: 2, disponible: true },
  { nom: 'Base Salon-de-Provence', latitude: 43.6061, longitude: 5.1089, capacite: 3, disponible: true },
  { nom: 'Base Avord', latitude: 47.0531, longitude: 2.6394, capacite: 2, disponible: true },
  { nom: 'Base Cazaux', latitude: 44.5333, longitude: -1.1250, capacite: 3, disponible: true },
  
  // Aéroports civils utilisables
  { nom: 'Aéroport Montpellier-Méditerranée', latitude: 43.5761, longitude: 3.9631, capacite: 2, disponible: true },
  { nom: 'Aéroport Toulouse-Blagnac', latitude: 43.6289, longitude: 1.3678, capacite: 2, disponible: true },
  { nom: 'Aéroport Bordeaux-Mérignac', latitude: 44.8283, longitude: -0.7156, capacite: 2, disponible: true },
  { nom: 'Aéroport Lyon-Saint-Exupéry', latitude: 45.7256, longitude: 5.0811, capacite: 2, disponible: true },
  { nom: 'Aéroport Nice-Côte d\'Azur', latitude: 43.6584, longitude: 7.2159, capacite: 2, disponible: true }
];

async function insertCommunes() {
  console.log('Insertion des communes...');
  
  const { data, error } = await supabase
    .from('communes')
    .insert(communesData);
    
  if (error) {
    console.error('Erreur lors de l\'insertion des communes:', error);
  } else {
    console.log(`✅ ${communesData.length} communes insérées avec succès`);
  }
}

async function insertPlansEau() {
  console.log('Insertion des plans d\'eau...');
  
  const { data, error } = await supabase
    .from('plans_eau')
    .insert(plansEauData);
    
  if (error) {
    console.error('Erreur lors de l\'insertion des plans d\'eau:', error);
  } else {
    console.log(`✅ ${plansEauData.length} plans d'eau insérés avec succès`);
  }
}

async function insertBasesDash() {
  console.log('Insertion des bases Dash...');
  
  const { data, error } = await supabase
    .from('bases_dash')
    .insert(basesDashData);
    
  if (error) {
    console.error('Erreur lors de l\'insertion des bases Dash:', error);
  } else {
    console.log(`✅ ${basesDashData.length} bases Dash insérées avec succès`);
  }
}

async function clearTables() {
  console.log('Nettoyage des tables existantes...');
  
  await supabase.from('communes').delete().neq('id', '00000000-0000-0000-0000-000000000000');
  await supabase.from('plans_eau').delete().neq('id', '00000000-0000-0000-0000-000000000000');
  await supabase.from('bases_dash').delete().neq('id', '00000000-0000-0000-0000-000000000000');
  
  console.log('✅ Tables nettoyées');
}

async function populateAllData() {
  try {
    console.log('🚀 Début du peuplement des données Supabase...\n');
    
    // Nettoyer les tables existantes
    await clearTables();
    
    // Insérer les nouvelles données
    await insertCommunes();
    await insertPlansEau();
    await insertBasesDash();
    
    console.log('\n🎉 Toutes les données ont été insérées avec succès !');
    console.log('\nVous pouvez maintenant utiliser l\'application FireSplash.');
    
  } catch (error) {
    console.error('❌ Erreur lors du peuplement des données:', error);
  }
}

// Exécuter le script
populateAllData();