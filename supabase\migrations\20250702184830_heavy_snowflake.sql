/*
  # Mise à jour de la structure des plans d'eau

  1. Modifications de la table plans_eau
    - Ajout de la colonne `commune` (nom de la commune)
    - Ajout de la colonne `ecopage` (boolean pour indiquer si l'écopage est possible)
    - Modification de la colonne `accessible` pour être plus spécifique à l'écopage
    - Ajout d'index pour optimiser les recherches

  2. Migration des données existantes
    - Mise à jour des données existantes pour être compatibles avec la nouvelle structure

  3. Mise à jour des politiques RLS
    - Ajout de politiques pour la mise à jour et suppression des données
*/

-- Ajouter les nouvelles colonnes à la table plans_eau
DO $$
BEGIN
  -- Ajouter la colonne commune si elle n'existe pas
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'plans_eau' AND column_name = 'commune'
  ) THEN
    ALTER TABLE plans_eau ADD COLUMN commune text;
  END IF;

  -- Ajouter la colonne ecopage si elle n'existe pas
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'plans_eau' AND column_name = 'ecopage'
  ) THEN
    ALTER TABLE plans_eau ADD COLUMN ecopage boolean DEFAULT true;
  END IF;
END $$;

-- Mettre à jour les données existantes pour être compatibles
UPDATE plans_eau SET ecopage = accessible WHERE ecopage IS NULL;

-- Ajouter des index pour optimiser les recherches
CREATE INDEX IF NOT EXISTS idx_plans_eau_commune ON plans_eau(commune);
CREATE INDEX IF NOT EXISTS idx_plans_eau_ecopage ON plans_eau(ecopage);

-- Ajouter des politiques pour la gestion des données
DO $$
BEGIN
  -- Politique pour la mise à jour
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'plans_eau' AND policyname = 'Authenticated users can update plans_eau'
  ) THEN
    CREATE POLICY "Authenticated users can update plans_eau"
      ON plans_eau
      FOR UPDATE
      TO authenticated
      USING (true)
      WITH CHECK (true);
  END IF;

  -- Politique pour la suppression
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'plans_eau' AND policyname = 'Authenticated users can delete plans_eau'
  ) THEN
    CREATE POLICY "Authenticated users can delete plans_eau"
      ON plans_eau
      FOR DELETE
      TO authenticated
      USING (true);
  END IF;
END $$;

-- Ajouter un commentaire sur la table
COMMENT ON TABLE plans_eau IS 'Table des plans d''eau avec informations d''écopage';
COMMENT ON COLUMN plans_eau.commune IS 'Nom de la commune où se trouve le plan d''eau';
COMMENT ON COLUMN plans_eau.ecopage IS 'Indique si l''écopage est possible sur ce plan d''eau';
COMMENT ON COLUMN plans_eau.accessible IS 'Indique si le plan d''eau est accessible (peut être différent de l''écopage)';