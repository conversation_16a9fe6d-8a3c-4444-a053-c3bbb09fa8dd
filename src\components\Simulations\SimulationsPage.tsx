import React, { useState, useEffect } from 'react';
import { Archive, Calendar, MapPin, Plane, Trash2, Eye, AlertCircle } from 'lucide-react';
import { Simulation } from '../../types';
import { getUserSimulations, deleteSimulation } from '../../services/simulationService';

interface SimulationsPageProps {
  onViewSimulation?: (simulation: Simulation) => void;
}

export function SimulationsPage({ onViewSimulation }: SimulationsPageProps) {
  const [simulations, setSimulations] = useState<Simulation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);

  useEffect(() => {
    loadSimulations();
  }, []);

  const loadSimulations = async () => {
    try {
      setLoading(true);
      const data = await getUserSimulations();
      setSimulations(data);
    } catch (err) {
      console.error('Erreur lors du chargement des simulations:', err);
      setError('Erreur lors du chargement des simulations');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette simulation ?')) {
      return;
    }

    try {
      setDeleteLoading(id);
      await deleteSimulation(id);
      setSimulations(prev => prev.filter(sim => sim.id !== id));
    } catch (err) {
      console.error('Erreur lors de la suppression:', err);
      alert('Erreur lors de la suppression de la simulation');
    } finally {
      setDeleteLoading(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-md p-12 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement des simulations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center space-x-3">
          <Archive className="h-8 w-8 text-red-600" />
          <span>Simulations sauvegardées</span>
        </h2>
        <p className="text-gray-600">
          Consultez et gérez vos simulations d'estimation de moyens aériens
        </p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center space-x-2">
            <AlertCircle className="h-5 w-5 text-red-600" />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      {simulations.length === 0 ? (
        <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-12 text-center">
          <Archive className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Aucune simulation sauvegardée
          </h3>
          <p className="text-gray-600">
            Effectuez une estimation et sauvegardez-la pour la retrouver ici
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {simulations.map((simulation) => (
            <div key={simulation.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 truncate">
                    {simulation.nom}
                  </h3>
                  <div className="flex space-x-2 ml-2">
                    {onViewSimulation && (
                      <button
                        onClick={() => onViewSimulation(simulation)}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        title="Voir la simulation"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                    )}
                    <button
                      onClick={() => handleDelete(simulation.id)}
                      disabled={deleteLoading === simulation.id}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50"
                      title="Supprimer"
                    >
                      {deleteLoading === simulation.id ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                      ) : (
                        <Trash2 className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </div>

                {simulation.description && (
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {simulation.description}
                  </p>
                )}

                <div className="space-y-3">
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <MapPin className="h-4 w-4" />
                    <span>{simulation.commune_data.nom} ({simulation.commune_data.departement})</span>
                  </div>

                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(simulation.created_at)}</span>
                  </div>

                  <div className="grid grid-cols-2 gap-4 pt-3 border-t">
                    <div className="text-center">
                      <div className="flex items-center justify-center space-x-1 text-blue-600 mb-1">
                        <Plane className="h-4 w-4" />
                        <span className="text-xs font-medium">Canadair</span>
                      </div>
                      <div className="text-lg font-bold text-blue-900">
                        {Math.ceil(simulation.results.canadairsExact)}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center space-x-1 text-green-600 mb-1">
                        <Plane className="h-4 w-4" />
                        <span className="text-xs font-medium">Dash</span>
                      </div>
                      <div className="text-lg font-bold text-green-900">
                        {Math.ceil(simulation.results.dashsExact)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
