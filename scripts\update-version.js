#!/usr/bin/env node

/**
 * Script pour synchroniser la version entre package.json et src/config/version.ts
 * Usage: node scripts/update-version.js [nouvelle-version]
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Chemins des fichiers
const packageJsonPath = path.join(__dirname, '../package.json');
const versionConfigPath = path.join(__dirname, '../src/config/version.ts');

function updateVersion(newVersion) {
  try {
    // Lire package.json
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    if (newVersion) {
      // Mettre à jour package.json
      packageJson.version = newVersion;
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
      console.log(`✅ package.json mis à jour vers v${newVersion}`);
    }

    // Lire le fichier de configuration de version
    let versionConfig = fs.readFileSync(versionConfigPath, 'utf8');
    
    // Remplacer la version dans le fichier de configuration
    const versionRegex = /export const APP_VERSION = '[^']+';/;
    const newVersionLine = `export const APP_VERSION = '${packageJson.version}';`;
    
    if (versionRegex.test(versionConfig)) {
      versionConfig = versionConfig.replace(versionRegex, newVersionLine);
    } else {
      console.error('❌ Impossible de trouver la ligne APP_VERSION dans version.ts');
      return;
    }
    
    // Écrire le fichier mis à jour
    fs.writeFileSync(versionConfigPath, versionConfig);
    console.log(`✅ src/config/version.ts mis à jour vers v${packageJson.version}`);
    
    console.log(`\n🎉 Version synchronisée: v${packageJson.version}`);
    
  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour de la version:', error.message);
    process.exit(1);
  }
}

// Récupérer la nouvelle version depuis les arguments de ligne de commande
const newVersion = process.argv[2];

if (newVersion && !/^\d+\.\d+\.\d+$/.test(newVersion)) {
  console.error('❌ Format de version invalide. Utilisez le format sémantique: x.y.z');
  console.log('Exemple: node scripts/update-version.js 1.3.0');
  process.exit(1);
}

updateVersion(newVersion);
