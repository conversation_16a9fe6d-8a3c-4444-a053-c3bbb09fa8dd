-- Script SQL direct pour insérer les données dans Supabase
-- <PERSON><PERSON> pouvez copier-coller ce script dans l'éditeur SQL de Supabase

-- Nettoyer les tables existantes
DELETE FROM communes;
DELETE FROM plans_eau;
DELETE FROM bases_dash;

-- Insérer les communes
INSERT INTO communes (nom, code_postal, latitude, longitude, departement) VALUES
-- Région Occitanie
('Nîmes', '30000', 43.8367, 4.3601, '<PERSON><PERSON>'),
('Montpellier', '34000', 43.6110, 3.8767, 'Hérault'),
('Toulouse', '31000', 43.6047, 1.4442, 'Haute-Garonne'),
('Perpignan', '66000', 42.6886, 2.8946, 'Pyrénées-Orientales'),
('B<PERSON>zier<PERSON>', '34500', 43.3415, 3.2150, '<PERSON><PERSON><PERSON>'),
('Narbonne', '11100', 43.1839, 3.0032, 'Aude'),
('Carcassonne', '11000', 43.2130, 2.3491, 'Aude'),
('<PERSON><PERSON>', '30100', 44.1250, 4.0816, '<PERSON><PERSON>'),
('<PERSON><PERSON><PERSON>', '34200', 43.4030, 3.6970, '<PERSON><PERSON><PERSON>'),
('<PERSON><PERSON>', '34400', 43.6750, 4.1360, 'H<PERSON>ult'),

-- R<PERSON>gion PACA
('Marseille', '13000', 43.2965, 5.3698, '<PERSON>uches-du-<PERSON>hône'),
('Nice', '06000', 43.7102, 7.2620, 'Al<PERSON>-Maritimes'),
('Toulon', '83000', 43.1242, 5.9280, 'Var'),
('Aix-en-Provence', '13100', 43.5297, 5.4474, 'Bouches-du-Rhône'),
('Cannes', '06400', 43.5528, 7.0174, 'Alpes-Maritimes'),
('Antibes', '06600', 43.5804, 7.1251, 'Alpes-Maritimes'),
('Avignon', '84000', 43.9493, 4.8059, 'Vaucluse'),
('Arles', '13200', 43.6768, 4.6311, 'Bouches-du-Rhône'),
('Fréjus', '83600', 43.4331, 6.7368, 'Var'),
('Hyères', '83400', 43.1205, 6.1286, 'Var'),

-- Région Nouvelle-Aquitaine
('Bordeaux', '33000', 44.8378, -0.5792, 'Gironde'),
('Limoges', '87000', 45.8336, 1.2611, 'Haute-Vienne'),
('Poitiers', '86000', 46.5802, 0.3404, 'Vienne'),
('Pau', '64000', 43.2951, -0.3707, 'Pyrénées-Atlantiques'),
('La Rochelle', '17000', 46.1603, -1.1511, 'Charente-Maritime'),
('Bayonne', '64100', 43.4832, -1.4748, 'Pyrénées-Atlantiques'),
('Angoulême', '16000', 45.6484, 0.1561, 'Charente'),
('Périgueux', '24000', 45.1848, 0.7218, 'Dordogne'),
('Agen', '47000', 44.2028, 0.6169, 'Lot-et-Garonne'),
('Mont-de-Marsan', '40000', 43.8906, -0.4999, 'Landes'),

-- Corse
('Ajaccio', '20000', 41.9196, 8.7389, 'Corse-du-Sud'),
('Bastia', '20200', 42.7028, 9.4507, 'Haute-Corse'),
('Porto-Vecchio', '20137', 41.5914, 9.2806, 'Corse-du-Sud'),
('Calvi', '20260', 42.5679, 8.7581, 'Haute-Corse'),

-- Région Auvergne-Rhône-Alpes
('Lyon', '69000', 45.7640, 4.8357, 'Rhône'),
('Grenoble', '38000', 45.1885, 5.7245, 'Isère'),
('Saint-Étienne', '42000', 45.4397, 4.3872, 'Loire'),
('Valence', '26000', 44.9334, 4.8924, 'Drôme'),
('Privas', '07000', 44.7354, 4.5996, 'Ardèche'),

-- Autres communes importantes
('Digne-les-Bains', '04000', 44.0944, 6.2361, 'Alpes-de-Haute-Provence'),
('Gap', '05000', 44.5598, 6.0777, 'Hautes-Alpes'),
('Draguignan', '83300', 43.5382, 6.4678, 'Var'),
('Brignoles', '83170', 43.4044, 6.0586, 'Var'),
('Orange', '84100', 44.1364, 4.8089, 'Vaucluse'),
('Carpentras', '84200', 44.0550, 5.0481, 'Vaucluse'),
('Salon-de-Provence', '13300', 43.6403, 5.0979, 'Bouches-du-Rhône'),
('Istres', '13800', 43.5135, 4.9877, 'Bouches-du-Rhône'),
('Martigues', '13500', 43.4074, 5.0515, 'Bouches-du-Rhône');

-- Insérer les plans d'eau
INSERT INTO plans_eau (nom, latitude, longitude, type, accessible) VALUES
-- Mer Méditerranée
('Mer Méditerranée (Palavas-les-Flots)', 43.5308, 3.9269, 'mer', true),
('Mer Méditerranée (La Grande-Motte)', 43.5619, 4.0828, 'mer', true),
('Mer Méditerranée (Saintes-Maries-de-la-Mer)', 43.4519, 4.4281, 'mer', true),
('Mer Méditerranée (Marseille - Prado)', 43.2584, 5.3656, 'mer', true),
('Mer Méditerranée (Cassis)', 43.2148, 5.5389, 'mer', true),
('Mer Méditerranée (Toulon)', 43.1047, 5.9337, 'mer', true),
('Mer Méditerranée (Saint-Tropez)', 43.2677, 6.6407, 'mer', true),
('Mer Méditerranée (Cannes)', 43.5483, 7.0085, 'mer', true),
('Mer Méditerranée (Nice)', 43.6951, 7.2758, 'mer', true),
('Mer Méditerranée (Ajaccio)', 41.9196, 8.7389, 'mer', true),
('Mer Méditerranée (Bastia)', 42.7028, 9.4507, 'mer', true),
('Mer Méditerranée (Porto-Vecchio)', 41.5914, 9.2806, 'mer', true),

-- Étangs et lacs
('Étang de Berre', 43.4581, 5.0892, 'etang', true),
('Étang de Thau', 43.4167, 3.6000, 'etang', true),
('Lac de Sainte-Croix', 43.7750, 6.2000, 'lac', true),
('Lac de Serre-Ponçon', 44.5167, 6.3500, 'lac', true),
('Lac du Bourget', 45.7167, 5.8500, 'lac', true),
('Lac Léman (Évian)', 46.4000, 6.5833, 'lac', true),
('Lac de Cazaux-Sanguinet', 44.5167, -1.1167, 'lac', true),
('Lac de Biscarrosse', 44.4167, -1.2333, 'lac', true),

-- Océan Atlantique
('Océan Atlantique (Arcachon)', 44.6667, -1.1667, 'mer', true),
('Océan Atlantique (Biarritz)', 43.4832, -1.5586, 'mer', true),
('Océan Atlantique (La Rochelle)', 46.1603, -1.1511, 'mer', true),
('Océan Atlantique (Royan)', 45.6167, -1.0333, 'mer', true),

-- Rivières
('Rhône (Arles)', 43.6768, 4.6311, 'riviere', true),
('Garonne (Bordeaux)', 44.8378, -0.5792, 'riviere', true),
('Loire (Nantes)', 47.2184, -1.5536, 'riviere', true);

-- Insérer les bases Dash
INSERT INTO bases_dash (nom, latitude, longitude, capacite, disponible) VALUES
-- Bases principales de la Sécurité Civile
('Base Nîmes-Garons', 43.7567, 4.4164, 6, true),
('Base Marignane', 43.4355, 5.2148, 4, true),
('Base Perpignan-Rivesaltes', 42.7397, 2.8706, 3, true),
('Base Ajaccio-Campo dell''Oro', 41.9236, 8.7928, 2, true),
('Base Solenzara', 41.9244, 9.4061, 2, true),

-- Bases militaires
('Base Istres-Le Tubé', 43.5227, 4.9236, 4, true),
('Base Orange-Caritat', 44.1425, 4.8678, 2, true),
('Base Salon-de-Provence', 43.6061, 5.1089, 3, true),
('Base Avord', 47.0531, 2.6394, 2, true),
('Base Cazaux', 44.5333, -1.1250, 3, true),

-- Aéroports civils
('Aéroport Montpellier-Méditerranée', 43.5761, 3.9631, 2, true),
('Aéroport Toulouse-Blagnac', 43.6289, 1.3678, 2, true),
('Aéroport Bordeaux-Mérignac', 44.8283, -0.7156, 2, true),
('Aéroport Lyon-Saint-Exupéry', 45.7256, 5.0811, 2, true),
('Aéroport Nice-Côte d''Azur', 43.6584, 7.2159, 2, true);

-- Vérifier les insertions
SELECT 'Communes' as table_name, COUNT(*) as count FROM communes
UNION ALL
SELECT 'Plans d''eau' as table_name, COUNT(*) as count FROM plans_eau
UNION ALL
SELECT 'Bases Dash' as table_name, COUNT(*) as count FROM bases_dash;