import React, { useState } from 'react';
import { Info, X } from 'lucide-react';
import { getFormattedVersion, VERSION_INFO, getBuildInfo } from '../../config/version';

interface VersionDisplayProps {
  position?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
  showDetails?: boolean;
  className?: string;
}

export function VersionDisplay({ 
  position = 'bottom-right', 
  showDetails = true,
  className = '' 
}: VersionDisplayProps) {
  const [showModal, setShowModal] = useState(false);
  const buildInfo = getBuildInfo();

  const positionClasses = {
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4'
  };

  return (
    <>
      {/* Bouton de version */}
      <div className={`fixed ${positionClasses[position]} z-40 ${className}`}>
        <button
          onClick={() => setShowModal(true)}
          className="flex items-center space-x-1 px-2 py-1 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-md shadow-sm text-xs text-gray-600 hover:text-gray-800 transition-all duration-200 border border-gray-200"
          title="Informations de version"
        >
          <Info className="h-3 w-3" />
          <span>{getFormattedVersion()}</span>
        </button>
      </div>

      {/* Modal d'informations détaillées */}
      {showModal && showDetails && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-96 overflow-y-auto">
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-semibold text-gray-900">
                {VERSION_INFO.fullName}
              </h3>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="p-4 space-y-4">
              {/* Informations de build */}
              <div>
                <h4 className="font-medium text-gray-800 mb-2">Informations de build</h4>
                <div className="space-y-1 text-sm text-gray-600">
                  <div><strong>Version :</strong> {buildInfo.version}</div>
                  <div><strong>Date de build :</strong> {buildInfo.buildDate}</div>
                  <div><strong>Environnement :</strong> {buildInfo.environment}</div>
                </div>
              </div>

              {/* Fonctionnalités actuelles */}
              <div>
                <h4 className="font-medium text-gray-800 mb-2">Fonctionnalités</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {VERSION_INFO.features.map((feature, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <span className="text-green-500 mt-0.5">•</span>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Changelog */}
              <div>
                <h4 className="font-medium text-gray-800 mb-2">Historique des versions</h4>
                <div className="space-y-3">
                  {Object.entries(VERSION_INFO.changelog).map(([version, changes]) => (
                    <div key={version}>
                      <h5 className="font-medium text-sm text-gray-700">v{version}</h5>
                      <ul className="text-xs text-gray-600 space-y-0.5 ml-2">
                        {changes.map((change, index) => (
                          <li key={index} className="flex items-start space-x-1">
                            <span className="text-blue-500 mt-0.5">•</span>
                            <span>{change}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="p-4 border-t bg-gray-50">
              <button
                onClick={() => setShowModal(false)}
                className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                Fermer
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

/**
 * Version simplifiée pour affichage inline
 */
export function InlineVersion({ className = '' }: { className?: string }) {
  return (
    <span className={`text-xs text-gray-500 ${className}`}>
      {getFormattedVersion()}
    </span>
  );
}

/**
 * Version pour footer
 */
export function FooterVersion({ className = '' }: { className?: string }) {
  return (
    <div className={`text-center text-xs text-gray-500 ${className}`}>
      {VERSION_INFO.fullName} • Build {getBuildInfo().buildDate}
    </div>
  );
}
