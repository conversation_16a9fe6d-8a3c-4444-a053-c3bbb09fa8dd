import React from 'react';
import { Tooltip } from './Tooltip';

interface ParameterInputProps {
  label: string;
  value: number;
  min: number;
  max: number;
  step: number;
  unit: string;
  onChange: (value: number) => void;
  tooltip?: string;
}

export function ParameterInput({
  label,
  value,
  min,
  max,
  step,
  unit,
  onChange,
  tooltip
}: ParameterInputProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseFloat(e.target.value);
    if (!isNaN(newValue) && newValue >= min && newValue <= max) {
      onChange(newValue);
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center space-x-2">
        <label className="text-sm font-medium text-gray-700">{label}</label>
        {tooltip && <Tooltip content={tooltip} />}
      </div>
      
      <div className="relative">
        <input
          type="number"
          value={value}
          min={min}
          max={max}
          step={step}
          onChange={handleChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 pr-16"
        />
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <span className="text-sm text-gray-500">{unit}</span>
        </div>
      </div>
      
      <div className="text-xs text-gray-400">
        Plage: {min} - {max} {unit}
      </div>
    </div>
  );
}