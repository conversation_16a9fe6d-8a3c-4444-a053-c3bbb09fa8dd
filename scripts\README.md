# Script de peuplement des données Supabase

Ce script permet de remplir automatiquement les tables Supabase avec des données réalistes pour l'application FireSplash.

## Configuration

1. **Récupérez vos clés Supabase :**
   - Connectez-vous à votre dashboard Supabase
   - Allez dans Settings > API
   - Copiez l'URL du projet et la clé "service_role" (pas la clé anon)

2. **Modifiez le script :**
   - <PERSON><PERSON><PERSON><PERSON> `scripts/populateData.js`
   - Remplacez `YOUR_SUPABASE_URL` par votre URL Supabase
   - Remplacez `YOUR_SUPABASE_SERVICE_ROLE_KEY` par votre clé service role

## Exécution

```bash
# Depuis la racine du projet
node scripts/populateData.js
```

## Données insérées

### Communes (45 communes)
- Principales villes du Sud de la France (zones à risque d'incendie)
- Coordonnées GPS précises
- Codes postaux et départements

### Plans d'eau (25 points de puisage)
- <PERSON><PERSON> (12 points de puisage)
- Océan Atlantique (4 points)
- Lacs et étangs (7 points)
- Rivières principales (2 points)

### Bases aériennes (15 bases)
- Bases de la Sécurité Civile (5 bases principales)
- Bases militaires utilisables (5 bases)
- Aéroports civils (5 aéroports)

## Vérification

Après l'exécution du script, vérifiez dans votre dashboard Supabase que les tables sont bien remplies :
- Table `communes` : 45 entrées
- Table `plans_eau` : 25 entrées  
- Table `bases_dash` : 15 entrées

## Sécurité

⚠️ **Important :** Utilisez la clé service_role uniquement pour ce script d'initialisation. Ne la commitez jamais dans votre code source !