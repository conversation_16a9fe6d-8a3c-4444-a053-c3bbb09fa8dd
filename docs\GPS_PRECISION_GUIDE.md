# Guide de précision GPS pour FireSplash

## Comprendre la précision des coordonnées GPS

### Précision par nombre de décimales

| Décimales | Précision approximative | Usage recommandé |
|-----------|------------------------|------------------|
| 0 | ~111 km | Pays/continents |
| 1 | ~11.1 km | Grandes régions |
| 2 | ~1.1 km | Villes |
| 3 | ~111 m | Quartiers |
| 4 | ~11.1 m | Parcelles |
| 5 | ~1.1 m | Arbres, bâtiments |
| 6 | ~11 cm | **Recommandé pour FireSplash** |
| 7 | ~1.1 cm | **Optimal pour FireSplash** |
| 8 | ~1.1 mm | Applications de précision |

## Configuration recommandée pour FireSplash

### Types PostgreSQL optimaux

```sql
-- Pour latitude (-90 à +90)
latitude NUMERIC(10,7)  -- 3 chiffres avant + 7 après = précision ~1cm

-- Pour longitude (-180 à +180) 
longitude NUMERIC(11,7) -- 4 chiffres avant + 7 après = précision ~1cm
```

### Pourquoi cette configuration ?

1. **Précision suffisante** : 1cm est largement suffisant pour localiser des plans d'eau et bases aériennes
2. **Performance optimale** : Évite le stockage de précision inutile
3. **Compatibilité** : Fonctionne avec tous les systèmes GPS standards
4. **Calculs efficaces** : Optimise les calculs de distance

## Validation des coordonnées

### Contraintes de validation

```sql
-- Latitude valide
CHECK (latitude >= -90 AND latitude <= 90)

-- Longitude valide  
CHECK (longitude >= -180 AND longitude <= 180)
```

### Exemples de coordonnées valides

```sql
-- France métropolitaine
latitude: 43.1234567   -- 7 décimales = précision ~1cm
longitude: 4.9876543   -- 7 décimales = précision ~1cm

-- Corse
latitude: 42.1234567
longitude: 9.1234567

-- Outre-mer (Guyane)
latitude: 4.1234567
longitude: -52.1234567
```

## Impact sur les performances

### Avantages de NUMERIC(10,7) / NUMERIC(11,7)

✅ **Stockage optimisé** : Pas de gaspillage d'espace
✅ **Index efficaces** : Recherches géographiques rapides  
✅ **Calculs précis** : Distance exacte au centimètre près
✅ **Compatibilité** : Fonctionne avec tous les outils cartographiques

### Comparaison avec d'autres types

| Type | Avantages | Inconvénients |
|------|-----------|---------------|
| `NUMERIC(10,7)` | ✅ Précision contrôlée<br>✅ Validation automatique | ⚠️ Légèrement plus lourd que FLOAT |
| `FLOAT8/DOUBLE` | ✅ Rapide<br>✅ Standard | ❌ Précision variable<br>❌ Erreurs d'arrondi |
| `NUMERIC` (sans précision) | ✅ Précision illimitée | ❌ Gaspillage de stockage<br>❌ Performances dégradées |

## Migration et import de données

### Étapes recommandées

1. **Appliquer la migration** de précision
2. **Valider les données existantes** avec le script d'analyse
3. **Tester l'import CSV** avec vos données réelles
4. **Vérifier les calculs** de distance

### Script de validation

```bash
# Analyser la précision actuelle
node scripts/validateCoordinatesPrecision.js

# Appliquer la migration SQL
# (Copier le contenu dans l'éditeur SQL Supabase)

# Tester l'import CSV
node scripts/importPlansEau.js ./data/test.csv
```

## Cas particuliers

### Coordonnées très précises (>7 décimales)

Si vos données sources ont plus de 7 décimales :

```sql
-- Configuration haute précision (si nécessaire)
latitude NUMERIC(13,10)   -- 10 décimales = précision ~0.01mm
longitude NUMERIC(14,10)  -- 10 décimales = précision ~0.01mm
```

⚠️ **Attention** : Une précision excessive peut :
- Ralentir les performances
- Augmenter l'usage de stockage
- Créer une fausse impression de précision

### Données manquantes

```sql
-- Gérer les coordonnées manquantes
latitude NUMERIC(10,7) NULL    -- Permettre NULL
longitude NUMERIC(10,7) NULL   -- Permettre NULL

-- Contrainte conditionnelle
CHECK (
  (latitude IS NULL AND longitude IS NULL) OR 
  (latitude IS NOT NULL AND longitude IS NOT NULL)
)
```

## Recommandations finales

### Pour FireSplash

1. **Utilisez NUMERIC(10,7) et NUMERIC(11,7)** pour un équilibre optimal
2. **Validez vos données** avant l'import
3. **Testez les calculs** de distance après migration
4. **Surveillez les performances** lors des requêtes géographiques

### Maintenance

- **Vérifiez périodiquement** la qualité des coordonnées
- **Nettoyez les doublons** géographiques
- **Optimisez les index** selon l'usage
- **Documentez les sources** de données GPS