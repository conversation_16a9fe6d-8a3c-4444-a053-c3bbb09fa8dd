import React, { useState, useEffect, useRef } from 'react';
import { Search, MapPin } from 'lucide-react';
import { Commune } from '../../types';
import { searchCommunes } from '../../services/supabaseService';

interface CommuneSearchProps {
  onCommuneSelect: (commune: Commune) => void;
  selectedCommune?: Commune;
}

export function CommuneSearch({ onCommuneSelect, selectedCommune }: CommuneSearchProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCommunes, setFilteredCommunes] = useState<Commune[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionRefs = useRef<(HTMLButtonElement | null)[]>([]);

  useEffect(() => {
    const searchCommunesAsync = async () => {
      if (searchTerm.length > 2) {
        setLoading(true);
        try {
          const communes = await searchCommunes(searchTerm);
          setFilteredCommunes(communes);
          setShowSuggestions(true);
          setSelectedIndex(-1); // Reset selection
        } catch (error) {
          console.error('Erreur lors de la recherche:', error);
          setFilteredCommunes([]);
        } finally {
          setLoading(false);
        }
      } else {
        setShowSuggestions(false);
        setFilteredCommunes([]);
        setSelectedIndex(-1);
      }
    };

    const timeoutId = setTimeout(searchCommunesAsync, 300); // Debounce
    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Mettre à jour les refs des suggestions
  useEffect(() => {
    suggestionRefs.current = suggestionRefs.current.slice(0, filteredCommunes.length);
  }, [filteredCommunes]);

  const handleCommuneSelect = (commune: Commune) => {
    setSearchTerm(commune.nom);
    setShowSuggestions(false);
    setSelectedIndex(-1);
    onCommuneSelect(commune);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || filteredCommunes.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => {
          const newIndex = prev < filteredCommunes.length - 1 ? prev + 1 : 0;
          // Focus sur l'élément sélectionné
          setTimeout(() => {
            suggestionRefs.current[newIndex]?.focus();
          }, 0);
          return newIndex;
        });
        break;

      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => {
          const newIndex = prev > 0 ? prev - 1 : filteredCommunes.length - 1;
          // Focus sur l'élément sélectionné
          setTimeout(() => {
            suggestionRefs.current[newIndex]?.focus();
          }, 0);
          return newIndex;
        });
        break;

      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < filteredCommunes.length) {
          handleCommuneSelect(filteredCommunes[selectedIndex]);
        }
        break;

      case 'Escape':
        e.preventDefault();
        setShowSuggestions(false);
        setSelectedIndex(-1);
        inputRef.current?.focus();
        break;

      case 'Tab':
        // Permettre la navigation normale par Tab
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
    }
  };

  const handleSuggestionKeyDown = (e: React.KeyboardEvent, index: number) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        const nextIndex = index < filteredCommunes.length - 1 ? index + 1 : 0;
        setSelectedIndex(nextIndex);
        suggestionRefs.current[nextIndex]?.focus();
        break;

      case 'ArrowUp':
        e.preventDefault();
        const prevIndex = index > 0 ? index - 1 : filteredCommunes.length - 1;
        setSelectedIndex(prevIndex);
        suggestionRefs.current[prevIndex]?.focus();
        break;

      case 'Enter':
      case ' ':
        e.preventDefault();
        handleCommuneSelect(filteredCommunes[index]);
        break;

      case 'Escape':
        e.preventDefault();
        setShowSuggestions(false);
        setSelectedIndex(-1);
        inputRef.current?.focus();
        break;

      case 'Tab':
        // Permettre la navigation normale par Tab
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
    }
  };

  const handleInputFocus = () => {
    if (searchTerm.length > 2 && filteredCommunes.length > 0) {
      setShowSuggestions(true);
    }
  };

  const handleInputBlur = (e: React.FocusEvent) => {
    // Ne fermer les suggestions que si le focus ne va pas vers une suggestion
    const relatedTarget = e.relatedTarget as HTMLElement;
    if (!relatedTarget || !relatedTarget.closest('[data-suggestion-list]')) {
      setTimeout(() => {
        setShowSuggestions(false);
        setSelectedIndex(-1);
      }, 150);
    }
  };

  return (
    <div className="relative">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Commune d'intervention
      </label>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        <input
          ref={inputRef}
          type="text"
          className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
          placeholder="Rechercher une commune..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          onKeyDown={handleKeyDown}
          autoComplete="off"
          role="combobox"
          aria-expanded={showSuggestions}
          aria-haspopup="listbox"
          aria-autocomplete="list"
          aria-activedescendant={selectedIndex >= 0 ? `suggestion-${selectedIndex}` : undefined}
        />
        {loading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
          </div>
        )}
      </div>

      {showSuggestions && filteredCommunes.length > 0 && (
        <div 
          className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
          data-suggestion-list
          role="listbox"
        >
          {filteredCommunes.map((commune, index) => (
            <button
              key={commune.id}
              ref={(el) => (suggestionRefs.current[index] = el)}
              className={`w-full px-4 py-2 text-left hover:bg-gray-50 focus:bg-blue-50 focus:outline-none flex items-center space-x-2 border-b border-gray-100 last:border-b-0 ${
                selectedIndex === index ? 'bg-blue-50' : ''
              }`}
              onClick={() => handleCommuneSelect(commune)}
              onKeyDown={(e) => handleSuggestionKeyDown(e, index)}
              role="option"
              aria-selected={selectedIndex === index}
              id={`suggestion-${index}`}
              tabIndex={-1}
            >
              <MapPin className="h-4 w-4 text-gray-400 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <div className="font-medium truncate">{commune.nom}</div>
                <div className="text-sm text-gray-500 truncate">{commune.codePostal} - {commune.departement}</div>
              </div>
            </button>
          ))}
        </div>
      )}

      {showSuggestions && filteredCommunes.length === 0 && !loading && searchTerm.length > 2 && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg p-4 text-center text-gray-500">
          Aucune commune trouvée
        </div>
      )}

      {selectedCommune && (
        <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-md">
          <div className="flex items-center space-x-2">
            <MapPin className="h-4 w-4 text-green-600" />
            <span className="text-green-800 font-medium">
              {selectedCommune.nom} ({selectedCommune.codePostal})
            </span>
          </div>
        </div>
      )}

      {/* Instructions d'utilisation (masquées visuellement mais accessibles aux lecteurs d'écran) */}
      <div className="sr-only" aria-live="polite">
        {showSuggestions && filteredCommunes.length > 0 && (
          `${filteredCommunes.length} suggestions disponibles. Utilisez les flèches haut et bas pour naviguer, Entrée pour sélectionner, Échap pour fermer.`
        )}
      </div>
    </div>
  );
}