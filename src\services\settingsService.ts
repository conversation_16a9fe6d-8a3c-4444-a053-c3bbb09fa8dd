import { supabase } from '../lib/supabase';
import { SimulatorSettings } from '../types';

/**
 * Service pour gérer les paramètres utilisateur dans Supabase
 */

export async function saveUserSettings(settings: SimulatorSettings): Promise<void> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('Utilisateur non authentifié');
    }

    // Vérifier si l'utilisateur a déjà des paramètres
    const { data: existingSettings } = await supabase
      .from('user_settings')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (existingSettings) {
      // Mettre à jour les paramètres existants
      const { error } = await supabase
        .from('user_settings')
        .update({ 
          settings: settings,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id);

      if (error) {
        throw error;
      }
    } else {
      // Créer de nouveaux paramètres
      const { error } = await supabase
        .from('user_settings')
        .insert({
          user_id: user.id,
          settings: settings
        });

      if (error) {
        throw error;
      }
    }
  } catch (error) {
    console.error('Erreur lors de la sauvegarde des paramètres:', error);
    throw error;
  }
}

export async function loadUserSettings(): Promise<SimulatorSettings | null> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return null;
    }

    const { data, error } = await supabase
      .from('user_settings')
      .select('settings')
      .eq('user_id', user.id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // Aucun paramètre trouvé, c'est normal pour un nouvel utilisateur
        return null;
      }
      throw error;
    }

    return data?.settings as SimulatorSettings;
  } catch (error) {
    console.error('Erreur lors du chargement des paramètres:', error);
    return null;
  }
}

export async function deleteUserSettings(): Promise<void> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('Utilisateur non authentifié');
    }

    const { error } = await supabase
      .from('user_settings')
      .delete()
      .eq('user_id', user.id);

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Erreur lors de la suppression des paramètres:', error);
    throw error;
  }
}