/*
  # Create communes and plans d'eau tables

  1. New Tables
    - `communes`
      - `id` (uuid, primary key)
      - `nom` (text)
      - `code_postal` (text)
      - `latitude` (numeric)
      - `longitude` (numeric)
      - `departement` (text)
      - `created_at` (timestamp)
    - `plans_eau`
      - `id` (uuid, primary key)
      - `nom` (text)
      - `latitude` (numeric)
      - `longitude` (numeric)
      - `type` (text)
      - `accessible` (boolean)
      - `created_at` (timestamp)
    - `bases_dash`
      - `id` (uuid, primary key)
      - `nom` (text)
      - `latitude` (numeric)
      - `longitude` (numeric)
      - `capacite` (integer)
      - `disponible` (boolean)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users to read data
*/

-- Create communes table
CREATE TABLE IF NOT EXISTS communes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  nom text NOT NULL,
  code_postal text NOT NULL,
  latitude numeric NOT NULL,
  longitude numeric NOT NULL,
  departement text NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Create plans_eau table
CREATE TABLE IF NOT EXISTS plans_eau (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  nom text NOT NULL,
  latitude numeric NOT NULL,
  longitude numeric NOT NULL,
  type text NOT NULL CHECK (type IN ('mer', 'lac', 'etang', 'riviere')),
  accessible boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
);

-- Create bases_dash table
CREATE TABLE IF NOT EXISTS bases_dash (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  nom text NOT NULL,
  latitude numeric NOT NULL,
  longitude numeric NOT NULL,
  capacite integer DEFAULT 0,
  disponible boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE communes ENABLE ROW LEVEL SECURITY;
ALTER TABLE plans_eau ENABLE ROW LEVEL SECURITY;
ALTER TABLE bases_dash ENABLE ROW LEVEL SECURITY;

-- Create policies for reading data (public access for this use case)
CREATE POLICY "Anyone can read communes"
  ON communes
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Anyone can read plans_eau"
  ON plans_eau
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Anyone can read bases_dash"
  ON bases_dash
  FOR SELECT
  TO public
  USING (true);

-- Create policies for authenticated users to insert/update data
CREATE POLICY "Authenticated users can insert communes"
  ON communes
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Authenticated users can insert plans_eau"
  ON plans_eau
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Authenticated users can insert bases_dash"
  ON bases_dash
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_communes_nom ON communes(nom);
CREATE INDEX IF NOT EXISTS idx_communes_code_postal ON communes(code_postal);
CREATE INDEX IF NOT EXISTS idx_communes_location ON communes(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_plans_eau_location ON plans_eau(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_bases_dash_location ON bases_dash(latitude, longitude);