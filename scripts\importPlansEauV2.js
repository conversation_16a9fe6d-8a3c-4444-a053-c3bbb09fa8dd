import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Configuration Supabase - remplacez par vos vraies valeurs
const supabaseUrl = 'YOUR_SUPABASE_URL';
const supabaseKey = 'YOUR_SUPABASE_SERVICE_ROLE_KEY';

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Parse un fichier CSV simple (format: commune,ecopage,latitude,longitude)
 */
function parseCSV(csvContent) {
  const lines = csvContent.trim().split('\n');
  const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
  
  console.log(`📋 En-têtes détectés: ${headers.join(', ')}`);
  
  const data = [];
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;
    
    const values = line.split(',').map(v => v.trim());
    const row = {};
    
    headers.forEach((header, index) => {
      row[header] = values[index] || null;
    });
    
    data.push(row);
  }
  
  return data;
}

/**
 * Valide et convertit une coordonnée GPS
 */
function parseCoordinate(value) {
  if (!value || value === '' || value === 'null' || value === 'NULL') {
    return null;
  }
  
  const parsed = parseFloat(value.toString().trim());
  
  if (isNaN(parsed)) {
    return null;
  }
  
  return parsed;
}

/**
 * Valide les coordonnées GPS
 */
function validateCoordinates(latitude, longitude) {
  if (latitude === null || longitude === null) {
    return false;
  }
  
  if (latitude < -90 || latitude > 90) {
    return false;
  }
  
  if (longitude < -180 || longitude > 180) {
    return false;
  }
  
  return true;
}

/**
 * Nettoie et valide le nom d'une commune
 */
function cleanCommuneName(commune) {
  if (!commune || typeof commune !== 'string') {
    return null;
  }
  
  const cleaned = commune
    .trim()
    .replace(/[""'']/g, '')
    .replace(/\s+/g, ' ')
    .toUpperCase();
  
  if (cleaned === '' || cleaned === 'NULL' || cleaned === 'UNDEFINED') {
    return null;
  }
  
  return cleaned;
}

/**
 * Génère un nom unique pour le plan d'eau
 */
function generatePlanEauName(commune, ecopage, existingNames = new Set()) {
  if (!commune || commune.trim() === '') {
    let baseName = `Plan d'eau inconnu ${Date.now()}`;
    let counter = 1;
    let finalName = baseName;
    
    while (existingNames.has(finalName)) {
      finalName = `${baseName} #${counter}`;
      counter++;
    }
    
    return finalName;
  }
  
  const cleanCommune = commune.trim();
  const suffix = ecopage ? '(Écopage)' : '(Non écopage)';
  let baseName = `Plan d'eau ${cleanCommune} ${suffix}`;
  
  // Gérer les doublons
  let counter = 1;
  let finalName = baseName;
  
  while (existingNames.has(finalName)) {
    finalName = `${baseName} #${counter}`;
    counter++;
  }
  
  return finalName;
}

/**
 * Convertit les données CSV en format compatible avec la base
 */
function convertCSVToPlansEau(csvData) {
  const validPlansEau = [];
  const invalidEntries = [];
  const existingNames = new Set();
  
  csvData.forEach((row, index) => {
    const lineNumber = index + 2;
    
    // Extraire les données selon le nouveau format
    const communeRaw = row.commune;
    const ecopageRaw = row.ecopage;
    const latitudeRaw = row.latitude;
    const longitudeRaw = row.longitude;
    
    console.log(`🔍 Ligne ${lineNumber}: commune="${communeRaw}", ecopage="${ecopageRaw}", lat="${latitudeRaw}", lon="${longitudeRaw}"`);
    
    // Nettoyer et valider la commune
    const commune = cleanCommuneName(communeRaw);
    
    if (!commune) {
      invalidEntries.push({
        line: lineNumber,
        reason: `Commune manquante ou invalide: "${communeRaw}"`,
        data: row
      });
      return;
    }
    
    // Parser les coordonnées
    const latitude = parseCoordinate(latitudeRaw);
    const longitude = parseCoordinate(longitudeRaw);
    
    if (!validateCoordinates(latitude, longitude)) {
      invalidEntries.push({
        line: lineNumber,
        reason: `Coordonnées invalides (Lat: ${latitudeRaw}, Long: ${longitudeRaw})`,
        data: row
      });
      return;
    }
    
    // Déterminer l'écopage
    let ecopage = false;
    if (ecopageRaw) {
      const ecopageStr = ecopageRaw.toString().toLowerCase();
      ecopage = ecopageStr === 'true' || ecopageStr === '1' || ecopageStr === 'oui' || ecopageStr === 'yes';
    }
    
    // Générer un nom unique
    const nom = generatePlanEauName(commune, ecopage, existingNames);
    existingNames.add(nom);
    
    // Créer l'objet plan d'eau (sans la colonne type)
    const planEau = {
      nom: nom,
      commune: commune,
      latitude: latitude,
      longitude: longitude,
      accessible: true,
      ecopage: ecopage
    };
    
    console.log(`✅ Plan d'eau créé: "${nom}"`);
    validPlansEau.push(planEau);
  });
  
  return { validPlansEau, invalidEntries };
}

/**
 * Importe les plans d'eau depuis un fichier CSV (nouveau format)
 */
async function importPlansEauFromCSV(csvFilePath) {
  try {
    console.log('🚀 Début de l\'import des plans d\'eau (format: commune,ecopage,latitude,longitude)...\n');
    
    if (!fs.existsSync(csvFilePath)) {
      throw new Error(`Le fichier ${csvFilePath} n'existe pas`);
    }
    
    const csvContent = fs.readFileSync(csvFilePath, 'utf-8');
    console.log('✅ Fichier CSV lu avec succès');
    
    // Nettoyer le contenu
    const cleanContent = csvContent
      .replace(/^\uFEFF/, '')
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n');
    
    const csvData = parseCSV(cleanContent);
    console.log(`📊 ${csvData.length} lignes trouvées dans le CSV`);
    
    if (csvData.length === 0) {
      throw new Error('Aucune donnée trouvée dans le fichier CSV');
    }
    
    // Vérifier le format attendu
    const headers = Object.keys(csvData[0]);
    const requiredHeaders = ['commune', 'ecopage', 'latitude', 'longitude'];
    const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));
    
    if (missingHeaders.length > 0) {
      throw new Error(`En-têtes manquants: ${missingHeaders.join(', ')}. Format attendu: commune,ecopage,latitude,longitude`);
    }
    
    // Convertir les données
    const { validPlansEau, invalidEntries } = convertCSVToPlansEau(csvData);
    
    console.log(`🔄 ${validPlansEau.length} plans d'eau valides à importer`);
    
    if (invalidEntries.length > 0) {
      console.log(`⚠️ ${invalidEntries.length} entrées invalides ignorées:`);
      invalidEntries.forEach(entry => {
        console.log(`  • Ligne ${entry.line}: ${entry.reason}`);
      });
    }
    
    if (validPlansEau.length === 0) {
      console.log('❌ Aucun plan d\'eau valide trouvé');
      return;
    }
    
    // Afficher un aperçu
    console.log('\n📋 Aperçu des données à importer:');
    validPlansEau.slice(0, 3).forEach((planEau, index) => {
      console.log(`${index + 1}. "${planEau.nom}"`);
      console.log(`   Commune: ${planEau.commune}`);
      console.log(`   Coordonnées: ${planEau.latitude}, ${planEau.longitude}`);
      console.log(`   Écopage: ${planEau.ecopage ? 'Oui' : 'Non'}`);
    });
    
    // Validation finale
    const invalidNames = validPlansEau.filter(p => !p.nom || p.nom.trim() === '');
    if (invalidNames.length > 0) {
      throw new Error(`${invalidNames.length} plans d'eau ont des noms invalides`);
    }
    
    // Import par lots
    const batchSize = 50;
    let totalInserted = 0;
    
    for (let i = 0; i < validPlansEau.length; i += batchSize) {
      const batch = validPlansEau.slice(i, i + batchSize);
      
      console.log(`📤 Import du lot ${Math.floor(i/batchSize) + 1}/${Math.ceil(validPlansEau.length/batchSize)} (${batch.length} éléments)...`);
      
      const { data, error } = await supabase
        .from('plans_eau')
        .insert(batch);
      
      if (error) {
        console.error(`❌ Erreur lors de l'import du lot ${Math.floor(i/batchSize) + 1}:`, error.message);
        console.error('Détails:', error);
        
        // Afficher le contenu problématique
        console.log('📋 Contenu du lot problématique:');
        batch.forEach((item, idx) => {
          console.log(`  ${idx + 1}. Nom: "${item.nom}" | Commune: "${item.commune}"`);
        });
        
        throw error;
      }
      
      totalInserted += batch.length;
      console.log(`✅ Lot ${Math.floor(i/batchSize) + 1} importé avec succès`);
    }
    
    console.log(`\n🎉 ${totalInserted} plans d'eau importés avec succès !`);
    
    // Statistiques
    const ecopageCount = validPlansEau.filter(p => p.ecopage).length;
    const nonEcopageCount = validPlansEau.filter(p => !p.ecopage).length;
    
    console.log('\n📈 Statistiques finales:');
    console.log(`• Plans d'eau avec écopage: ${ecopageCount}`);
    console.log(`• Plans d'eau sans écopage: ${nonEcopageCount}`);
    console.log(`• Total importé: ${totalInserted}`);
    console.log(`• Entrées ignorées: ${invalidEntries.length}`);
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'import:', error.message);
    
    console.log('\n🔧 Conseils de dépannage:');
    console.log('• Vérifiez le format CSV: commune,ecopage,latitude,longitude');
    console.log('• Assurez-vous que les coordonnées sont des nombres valides');
    console.log('• Vérifiez que vos clés Supabase sont correctes');
    console.log('• Exemple de ligne valide: LES MUREAUX,false,49.001358940383795,1.9127045567936576');
    
    process.exit(1);
  }
}

/**
 * Valide un fichier CSV avant import
 */
async function validateCSV(csvFilePath) {
  try {
    console.log('🔍 Validation du fichier CSV...\n');
    
    if (!fs.existsSync(csvFilePath)) {
      throw new Error(`Le fichier ${csvFilePath} n'existe pas`);
    }
    
    const csvContent = fs.readFileSync(csvFilePath, 'utf-8');
    const cleanContent = csvContent
      .replace(/^\uFEFF/, '')
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n');
    
    const csvData = parseCSV(cleanContent);
    
    if (csvData.length === 0) {
      throw new Error('Aucune donnée trouvée');
    }
    
    const headers = Object.keys(csvData[0]);
    console.log(`📋 En-têtes détectés: ${headers.join(', ')}`);
    
    const requiredHeaders = ['commune', 'ecopage', 'latitude', 'longitude'];
    const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));
    
    if (missingHeaders.length > 0) {
      throw new Error(`En-têtes manquants: ${missingHeaders.join(', ')}`);
    }
    
    const { validPlansEau, invalidEntries } = convertCSVToPlansEau(csvData);
    
    console.log('✅ Validation réussie');
    console.log(`📊 ${csvData.length} lignes analysées`);
    console.log(`✅ ${validPlansEau.length} entrées valides`);
    console.log(`⚠️ ${invalidEntries.length} entrées invalides`);
    
    if (invalidEntries.length > 0) {
      console.log('\n❌ Erreurs détectées:');
      invalidEntries.forEach(entry => {
        console.log(`  • Ligne ${entry.line}: ${entry.reason}`);
      });
    }
    
    if (validPlansEau.length > 0) {
      console.log('\n📋 Aperçu des noms générés:');
      validPlansEau.slice(0, 5).forEach((planEau, index) => {
        console.log(`  ${index + 1}. "${planEau.nom}" (${planEau.commune})`);
      });
    }
    
    return { valid: validPlansEau.length > 0, validCount: validPlansEau.length, invalidCount: invalidEntries.length };
    
  } catch (error) {
    console.error('❌ Erreur de validation:', error.message);
    return { valid: false, error: error.message };
  }
}

// Script principal
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Usage: node importPlansEauV2.js <chemin_vers_fichier.csv> [options]');
    console.log('');
    console.log('Format CSV attendu: commune,ecopage,latitude,longitude');
    console.log('Exemple: LES MUREAUX,false,49.001358940383795,1.9127045567936576');
    console.log('');
    console.log('Options:');
    console.log('  --validate  Valide seulement le fichier sans importer');
    console.log('  --help      Affiche cette aide');
    console.log('');
    console.log('Exemples:');
    console.log('  node importPlansEauV2.js ./data/plans_eau.csv --validate');
    console.log('  node importPlansEauV2.js ./data/plans_eau.csv');
    process.exit(1);
  }
  
  const csvFilePath = args[0];
  const validateOnly = args.includes('--validate');
  
  if (validateOnly) {
    await validateCSV(csvFilePath);
    return;
  }
  
  await importPlansEauFromCSV(csvFilePath);
}

// Exécuter le script
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { importPlansEauFromCSV, validateCSV };