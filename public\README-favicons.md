# Favicons FireSplash

Ce dossier contient tous les favicons et icônes pour l'application FireSplash.

## Fichiers inclus

### Favicons principaux
- `favicon.svg` - Favicon principal en SVG (32x32, vectoriel)
- `favicon-16x16.svg` - Version optimisée pour 16x16 pixels
- `favicon-32x32.svg` - Version optimisée pour 32x32 pixels

### Icônes mobiles
- `apple-touch-icon.svg` - Icône pour iOS/Safari (180x180)

### Configuration
- `site.webmanifest` - Manifeste pour Progressive Web App

## Design

Le favicon reprend l'identité visuelle de FireSplash :
- **Couleur principale** : Rouge #DC2626 (même que l'interface)
- **Motif** : Flamme stylisée en blanc/jaune
- **Style** : Moderne avec gradients et effets d'ombre
- **Compatibilité** : Optimisé pour toutes les tailles d'écran

## Utilisation

Les favicons sont automatiquement chargés via le fichier `index.html` :

```html
<!-- Favicons -->
<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
<link rel="icon" type="image/svg+xml" sizes="16x16" href="/favicon-16x16.svg" />
<link rel="icon" type="image/svg+xml" sizes="32x32" href="/favicon-32x32.svg" />
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.svg" />
<link rel="manifest" href="/site.webmanifest" />
```

## Compatibilité

- ✅ **Navigateurs modernes** : SVG avec support complet
- ✅ **Safari/iOS** : Apple Touch Icon optimisé
- ✅ **PWA** : Manifeste avec icônes multiples tailles
- ✅ **Anciens navigateurs** : Fallback vers SVG principal

## Mise à jour

Pour modifier les favicons :
1. Éditer les fichiers SVG dans ce dossier
2. Maintenir la cohérence avec l'identité visuelle
3. Tester sur différents navigateurs et appareils
4. Mettre à jour le manifeste si nécessaire

## Génération automatique

Les favicons ont été créés pour correspondre exactement à l'icône Flame de Lucide React utilisée dans l'interface, garantissant une cohérence parfaite entre l'application et son icône.
