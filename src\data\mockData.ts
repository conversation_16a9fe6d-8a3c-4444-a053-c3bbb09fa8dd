import { Commune, PlanEau, BaseDash } from '../types';

export const mockCommunes: Commune[] = [
  {
    id: '1',
    nom: 'Nîmes',
    codePostal: '30000',
    latitude: 43.8367,
    longitude: 4.3601,
    departement: 'Gard'
  },
  {
    id: '2',
    nom: 'Montpellier',
    codePostal: '34000',
    latitude: 43.6110,
    longitude: 3.8767,
    departement: '<PERSON>érault'
  },
  {
    id: '3',
    nom: 'Arles',
    codePostal: '13200',
    latitude: 43.6768,
    longitude: 4.6311,
    departement: 'Bouches-du-Rhône'
  },
  {
    id: '4',
    nom: 'Avignon',
    codePostal: '84000',
    latitude: 43.9493,
    longitude: 4.8059,
    departement: 'Vaucluse'
  },
  {
    id: '5',
    nom: 'Perpignan',
    codePostal: '66000',
    latitude: 42.6886,
    longitude: 2.8946,
    departement: 'Pyrénées-Orientales'
  }
];

export const mockPlansEau: <PERSON><PERSON><PERSON>[] = [
  {
    id: '1',
    nom: '<PERSON><PERSON> (Palavas)',
    latitude: 43.5308,
    longitude: 3.9269,
    accessible: true,
    ecopage: true
  },
  {
    id: '2',
    nom: 'Étang de <PERSON>rre',
    latitude: 43.4581,
    longitude: 5.0892,
    accessible: true,
    ecopage: true
  },
  {
    id: '3',
    nom: 'Mer Méditerranée (La Grande-Motte)',
    latitude: 43.5619,
    longitude: 4.0828,
    accessible: true,
    ecopage: true
  },
  {
    id: '4',
    nom: 'Lac de Sainte-Croix',
    latitude: 43.7750,
    longitude: 6.2000,
    accessible: true,
    ecopage: true
  }
];

export const mockBasesDash: BaseDash[] = [
  {
    id: '1',
    nom: 'Base Nîmes-Garons',
    latitude: 43.7567,
    longitude: 4.4164,
    capacite: 4,
    disponible: true
  },
  {
    id: '2',
    nom: 'Base Marignane',
    latitude: 43.4355,
    longitude: 5.2148,
    capacite: 3,
    disponible: true
  },
  {
    id: '3',
    nom: 'Base Perpignan-Rivesaltes',
    latitude: 42.7397,
    longitude: 2.8706,
    capacite: 2,
    disponible: true
  }
];