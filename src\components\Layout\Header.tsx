import React, { useState } from 'react';
import { Flame, LogOut, User, Settings, Menu, X, Archive, Calculator } from 'lucide-react';
import { InlineVersion } from './VersionDisplay';

interface HeaderProps {
  user?: { email: string };
  onLogout: () => void;
  onSettingsClick?: () => void;
  onSimulationsClick?: () => void;
  onDashboardClick?: () => void;
  currentView?: string;
}

export function Header({
  user,
  onLogout,
  onSettingsClick,
  onSimulationsClick,
  onDashboardClick,
  currentView
}: HeaderProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <header className="bg-red-600 text-white shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-3">
            <Flame className="h-8 w-8" />
            <div>
              <div className="flex items-center space-x-2">
                <h1 className="text-xl font-bold">FireSplash</h1>
                <InlineVersion className="text-red-300" />
              </div>
              <p className="text-red-200 text-sm hidden sm:block">Estimation moyens aériens</p>
            </div>
          </div>

          {/* Navigation */}
          {user && (
            <nav className="hidden lg:flex items-center space-x-1">
              <button
                onClick={onDashboardClick}
                className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm transition-colors ${
                  currentView === 'dashboard'
                    ? 'bg-red-700 text-white'
                    : 'text-red-200 hover:text-white hover:bg-red-700'
                }`}
              >
                <Calculator className="h-4 w-4" />
                <span>Estimation</span>
              </button>
              <button
                onClick={onSimulationsClick}
                className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm transition-colors ${
                  currentView === 'simulations'
                    ? 'bg-red-700 text-white'
                    : 'text-red-200 hover:text-white hover:bg-red-700'
                }`}
              >
                <Archive className="h-4 w-4" />
                <span>Simulations</span>
              </button>
            </nav>
          )}
          
          {user && (
            <>
              {/* Desktop menu */}
              <div className="hidden lg:flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <User className="h-5 w-5" />
                  <span className="text-sm">{user.email}</span>
                </div>
                
                {onSettingsClick && (
                  <button
                    onClick={onSettingsClick}
                    className="flex items-center space-x-1 bg-red-700 hover:bg-red-800 px-3 py-2 rounded-md text-sm transition-colors"
                  >
                    <Settings className="h-4 w-4" />
                    <span>Paramètres</span>
                  </button>
                )}
                
                <button
                  onClick={onLogout}
                  className="flex items-center space-x-1 bg-red-700 hover:bg-red-800 px-3 py-2 rounded-md text-sm transition-colors"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Déconnexion</span>
                </button>
              </div>

              {/* Mobile menu button */}
              <div className="lg:hidden">
                <button
                  onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                  className="bg-red-700 hover:bg-red-800 p-2 rounded-md transition-colors"
                >
                  {mobileMenuOpen ? (
                    <X className="h-5 w-5" />
                  ) : (
                    <Menu className="h-5 w-5" />
                  )}
                </button>
              </div>
            </>
          )}
        </div>

        {/* Mobile menu */}
        {user && mobileMenuOpen && (
          <div className="lg:hidden border-t border-red-500 py-4 space-y-3">
            <div className="flex items-center space-x-2 px-2">
              <User className="h-4 w-4" />
              <span className="text-sm">{user.email}</span>
            </div>

            {/* Navigation mobile */}
            <button
              onClick={() => {
                onDashboardClick?.();
                setMobileMenuOpen(false);
              }}
              className={`flex items-center space-x-2 w-full text-left px-2 py-2 rounded-md transition-colors ${
                currentView === 'dashboard'
                  ? 'bg-red-700 text-white'
                  : 'hover:bg-red-700'
              }`}
            >
              <Calculator className="h-4 w-4" />
              <span>Estimation</span>
            </button>

            <button
              onClick={() => {
                onSimulationsClick?.();
                setMobileMenuOpen(false);
              }}
              className={`flex items-center space-x-2 w-full text-left px-2 py-2 rounded-md transition-colors ${
                currentView === 'simulations'
                  ? 'bg-red-700 text-white'
                  : 'hover:bg-red-700'
              }`}
            >
              <Archive className="h-4 w-4" />
              <span>Simulations</span>
            </button>

            {onSettingsClick && (
              <button
                onClick={() => {
                  onSettingsClick();
                  setMobileMenuOpen(false);
                }}
                className="flex items-center space-x-2 w-full text-left px-2 py-2 hover:bg-red-700 rounded-md transition-colors"
              >
                <Settings className="h-4 w-4" />
                <span>Paramètres</span>
              </button>
            )}
            
            <button
              onClick={() => {
                onLogout();
                setMobileMenuOpen(false);
              }}
              className="flex items-center space-x-2 w-full text-left px-2 py-2 hover:bg-red-700 rounded-md transition-colors"
            >
              <LogOut className="h-4 w-4" />
              <span>Déconnexion</span>
            </button>
          </div>
        )}
      </div>
    </header>
  );
}