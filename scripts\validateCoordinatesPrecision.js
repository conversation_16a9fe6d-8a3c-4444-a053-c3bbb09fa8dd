import { createClient } from '@supabase/supabase-js';

// Configuration Supabase
const supabaseUrl = 'YOUR_SUPABASE_URL';
const supabaseKey = 'YOUR_SUPABASE_SERVICE_ROLE_KEY';

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Analyse la précision des coordonnées dans toutes les tables
 */
async function analyzeCoordinatesPrecision() {
  console.log('🔍 Analyse de la précision des coordonnées GPS...\n');
  
  try {
    // Analyser les communes
    const { data: communes, error: communesError } = await supabase
      .from('communes')
      .select('nom, latitude, longitude');
    
    if (communesError) throw communesError;
    
    // Analyser les plans d'eau
    const { data: plansEau, error: plansEauError } = await supabase
      .from('plans_eau')
      .select('nom, latitude, longitude')
      .not('latitude', 'is', null);
    
    if (plansEauError) throw plansEauError;
    
    // Analyser les bases dash
    const { data: basesDash, error: basesDashError } = await supabase
      .from('bases_dash')
      .select('nom, latitude, longitude');
    
    if (basesDashError) throw basesDashError;
    
    // Fonction pour analyser la précision d'un nombre
    function getDecimalPlaces(num) {
      if (!num) return 0;
      const str = num.toString();
      if (str.indexOf('.') === -1) return 0;
      return str.split('.')[1].length;
    }
    
    // Analyser toutes les coordonnées
    const allCoords = [
      ...communes.map(c => ({ source: 'communes', nom: c.nom, lat: c.latitude, lon: c.longitude })),
      ...plansEau.map(p => ({ source: 'plans_eau', nom: p.nom, lat: p.latitude, lon: p.longitude })),
      ...basesDash.map(b => ({ source: 'bases_dash', nom: b.nom, lat: b.latitude, lon: b.longitude }))
    ];
    
    let maxLatPrecision = 0;
    let maxLonPrecision = 0;
    let totalCoords = 0;
    let highPrecisionCoords = [];
    
    allCoords.forEach(coord => {
      if (coord.lat && coord.lon) {
        totalCoords++;
        const latPrecision = getDecimalPlaces(coord.lat);
        const lonPrecision = getDecimalPlaces(coord.lon);
        
        maxLatPrecision = Math.max(maxLatPrecision, latPrecision);
        maxLonPrecision = Math.max(maxLonPrecision, lonPrecision);
        
        // Identifier les coordonnées avec une précision très élevée
        if (latPrecision > 10 || lonPrecision > 10) {
          highPrecisionCoords.push({
            ...coord,
            latPrecision,
            lonPrecision
          });
        }
      }
    });
    
    // Afficher les résultats
    console.log('📊 Résultats de l\'analyse:');
    console.log(`• Total de coordonnées analysées: ${totalCoords}`);
    console.log(`• Précision maximale latitude: ${maxLatPrecision} décimales`);
    console.log(`• Précision maximale longitude: ${maxLonPrecision} décimales`);
    
    // Calculer la précision géographique
    const latPrecisionMeters = calculatePrecisionInMeters(maxLatPrecision, 'latitude');
    const lonPrecisionMeters = calculatePrecisionInMeters(maxLonPrecision, 'longitude');
    
    console.log(`• Précision géographique latitude: ~${latPrecisionMeters}`);
    console.log(`• Précision géographique longitude: ~${lonPrecisionMeters}`);
    
    // Recommandations
    console.log('\n💡 Recommandations:');
    
    if (maxLatPrecision <= 7 && maxLonPrecision <= 7) {
      console.log('✅ La précision actuelle (≤7 décimales) est optimale pour les applications géographiques');
      console.log('✅ NUMERIC(10,7) et NUMERIC(11,7) sont appropriés');
    } else if (maxLatPrecision <= 10 && maxLonPrecision <= 10) {
      console.log('⚠️ Précision élevée détectée. Considérez NUMERIC(13,10) et NUMERIC(14,10)');
    } else {
      console.log('🔴 Précision très élevée détectée. Vérifiez si cette précision est nécessaire');
    }
    
    // Afficher les coordonnées avec précision très élevée
    if (highPrecisionCoords.length > 0) {
      console.log('\n🔍 Coordonnées avec précision très élevée (>10 décimales):');
      highPrecisionCoords.slice(0, 5).forEach(coord => {
        console.log(`• ${coord.source}: ${coord.nom} - Lat: ${coord.latPrecision}d, Lon: ${coord.lonPrecision}d`);
      });
      if (highPrecisionCoords.length > 5) {
        console.log(`... et ${highPrecisionCoords.length - 5} autres`);
      }
    }
    
    // Vérifier la structure actuelle de la base
    console.log('\n🔧 Vérification de la structure actuelle...');
    await checkCurrentTableStructure();
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'analyse:', error.message);
  }
}

/**
 * Calcule la précision en mètres selon le nombre de décimales
 */
function calculatePrecisionInMeters(decimals, type) {
  // Précision approximative pour les coordonnées GPS
  const precisionTable = {
    0: '~111 km',
    1: '~11.1 km', 
    2: '~1.1 km',
    3: '~111 m',
    4: '~11.1 m',
    5: '~1.1 m',
    6: '~11 cm',
    7: '~1.1 cm',
    8: '~1.1 mm',
    9: '~0.11 mm',
    10: '~0.011 mm'
  };
  
  return precisionTable[Math.min(decimals, 10)] || `~${Math.pow(10, -(decimals-2))} m`;
}

/**
 * Vérifie la structure actuelle des tables
 */
async function checkCurrentTableStructure() {
  try {
    const { data, error } = await supabase.rpc('get_column_info', {
      table_names: ['communes', 'plans_eau', 'bases_dash']
    });
    
    if (error) {
      // Si la fonction n'existe pas, utiliser une requête alternative
      console.log('ℹ️ Impossible de vérifier automatiquement la structure');
      console.log('ℹ️ Exécutez la migration pour optimiser la précision');
      return;
    }
    
    console.log('📋 Structure actuelle des colonnes de coordonnées:');
    data.forEach(col => {
      if (col.column_name === 'latitude' || col.column_name === 'longitude') {
        console.log(`• ${col.table_name}.${col.column_name}: ${col.data_type}`);
      }
    });
    
  } catch (error) {
    console.log('ℹ️ Structure actuelle: Vérifiez manuellement dans Supabase');
  }
}

/**
 * Teste l'insertion de coordonnées avec haute précision
 */
async function testHighPrecisionInsert() {
  console.log('\n🧪 Test d\'insertion avec haute précision...');
  
  const testCoord = {
    nom: 'Test Précision GPS',
    latitude: 43.1234567890123, // 13 décimales
    longitude: 4.9876543210987,  // 13 décimales
    type: 'lac',
    accessible: true,
    ecopage: true
  };
  
  try {
    const { data, error } = await supabase
      .from('plans_eau')
      .insert(testCoord)
      .select()
      .single();
    
    if (error) {
      console.log('❌ Erreur lors du test:', error.message);
      return;
    }
    
    console.log('✅ Test réussi');
    console.log(`• Latitude insérée: ${data.latitude}`);
    console.log(`• Longitude insérée: ${data.longitude}`);
    
    // Nettoyer le test
    await supabase.from('plans_eau').delete().eq('id', data.id);
    console.log('🧹 Données de test nettoyées');
    
  } catch (error) {
    console.log('❌ Erreur lors du test:', error.message);
  }
}

// Script principal
async function main() {
  await analyzeCoordinatesPrecision();
  await testHighPrecisionInsert();
  
  console.log('\n📝 Prochaines étapes:');
  console.log('1. Exécutez la migration SQL pour optimiser la précision');
  console.log('2. Testez l\'import de vos données CSV');
  console.log('3. Vérifiez que les calculs de distance restent précis');
}

// Exécuter le script
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { analyzeCoordinatesPrecision, testHighPrecisionInsert };