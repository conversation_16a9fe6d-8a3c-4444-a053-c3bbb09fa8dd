/*
  # Optimisation de la précision des coordonnées GPS

  1. Modifications
    - Définir une précision explicite pour latitude et longitude
    - Ajouter des contraintes de validation pour les coordonnées GPS
    - Optimiser les index géographiques

  2. Précision
    - NUMERIC(10,7) pour latitude : 7 décimales = précision ~1cm
    - NUMERIC(11,7) pour longitude : 7 décimales = précision ~1cm
    - Contraintes de validation pour les plages GPS valides
*/

-- Modifier la précision des coordonnées dans la table communes
DO $$
BEGIN
  -- Modifier latitude avec précision explicite
  ALTER TABLE communes ALTER COLUMN latitude TYPE NUMERIC(10,7);
  
  -- Modifier longitude avec précision explicite  
  ALTER TABLE communes ALTER COLUMN longitude TYPE NUMERIC(11,7);
  
  -- Ajouter des contraintes de validation pour les coordonnées GPS
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE table_name = 'communes' AND constraint_name = 'communes_latitude_check'
  ) THEN
    ALTER TABLE communes ADD CONSTRAINT communes_latitude_check 
    CHECK (latitude >= -90 AND latitude <= 90);
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE table_name = 'communes' AND constraint_name = 'communes_longitude_check'
  ) THEN
    ALTER TABLE communes ADD CONSTRAINT communes_longitude_check 
    CHECK (longitude >= -180 AND longitude <= 180);
  END IF;
END $$;

-- Modifier la précision des coordonnées dans la table plans_eau
DO $$
BEGIN
  -- Modifier latitude avec précision explicite
  ALTER TABLE plans_eau ALTER COLUMN latitude TYPE NUMERIC(10,7);
  
  -- Modifier longitude avec précision explicite
  ALTER TABLE plans_eau ALTER COLUMN longitude TYPE NUMERIC(11,7);
  
  -- Ajouter des contraintes de validation pour les coordonnées GPS
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE table_name = 'plans_eau' AND constraint_name = 'plans_eau_latitude_check'
  ) THEN
    ALTER TABLE plans_eau ADD CONSTRAINT plans_eau_latitude_check 
    CHECK (latitude >= -90 AND latitude <= 90);
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE table_name = 'plans_eau' AND constraint_name = 'plans_eau_longitude_check'
  ) THEN
    ALTER TABLE plans_eau ADD CONSTRAINT plans_eau_longitude_check 
    CHECK (longitude >= -180 AND longitude <= 180);
  END IF;
END $$;

-- Modifier la précision des coordonnées dans la table bases_dash
DO $$
BEGIN
  -- Modifier latitude avec précision explicite
  ALTER TABLE bases_dash ALTER COLUMN latitude TYPE NUMERIC(10,7);
  
  -- Modifier longitude avec précision explicite
  ALTER TABLE bases_dash ALTER COLUMN longitude TYPE NUMERIC(11,7);
  
  -- Ajouter des contraintes de validation pour les coordonnées GPS
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE table_name = 'bases_dash' AND constraint_name = 'bases_dash_latitude_check'
  ) THEN
    ALTER TABLE bases_dash ADD CONSTRAINT bases_dash_latitude_check 
    CHECK (latitude >= -90 AND latitude <= 90);
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE table_name = 'bases_dash' AND constraint_name = 'bases_dash_longitude_check'
  ) THEN
    ALTER TABLE bases_dash ADD CONSTRAINT bases_dash_longitude_check 
    CHECK (longitude >= -180 AND longitude <= 180);
  END IF;
END $$;

-- Recréer les index géographiques avec la nouvelle précision
DROP INDEX IF EXISTS idx_communes_location;
DROP INDEX IF EXISTS idx_plans_eau_location;
DROP INDEX IF EXISTS idx_bases_dash_location;

CREATE INDEX idx_communes_location ON communes USING btree (latitude, longitude);
CREATE INDEX idx_plans_eau_location ON plans_eau USING btree (latitude, longitude);
CREATE INDEX idx_bases_dash_location ON bases_dash USING btree (latitude, longitude);

-- Ajouter des commentaires explicatifs
COMMENT ON COLUMN communes.latitude IS 'Latitude GPS avec précision de 7 décimales (~1cm)';
COMMENT ON COLUMN communes.longitude IS 'Longitude GPS avec précision de 7 décimales (~1cm)';
COMMENT ON COLUMN plans_eau.latitude IS 'Latitude GPS avec précision de 7 décimales (~1cm)';
COMMENT ON COLUMN plans_eau.longitude IS 'Longitude GPS avec précision de 7 décimales (~1cm)';
COMMENT ON COLUMN bases_dash.latitude IS 'Latitude GPS avec précision de 7 décimales (~1cm)';
COMMENT ON COLUMN bases_dash.longitude IS 'Longitude GPS avec précision de 7 décimales (~1cm)';

-- Vérifier la précision des données existantes
DO $$
DECLARE
  max_lat_precision INTEGER;
  max_lon_precision INTEGER;
BEGIN
  -- Vérifier la précision maximale utilisée dans les données existantes
  SELECT 
    MAX(LENGTH(SPLIT_PART(latitude::TEXT, '.', 2))),
    MAX(LENGTH(SPLIT_PART(longitude::TEXT, '.', 2)))
  INTO max_lat_precision, max_lon_precision
  FROM (
    SELECT latitude, longitude FROM communes
    UNION ALL
    SELECT latitude, longitude FROM plans_eau WHERE latitude IS NOT NULL
    UNION ALL
    SELECT latitude, longitude FROM bases_dash
  ) AS all_coords;
  
  RAISE NOTICE 'Précision maximale actuelle - Latitude: % décimales, Longitude: % décimales', 
    COALESCE(max_lat_precision, 0), COALESCE(max_lon_precision, 0);
  
  IF max_lat_precision > 7 OR max_lon_precision > 7 THEN
    RAISE WARNING 'Certaines coordonnées ont plus de 7 décimales. Vérifiez si une précision plus élevée est nécessaire.';
  END IF;
END $$;