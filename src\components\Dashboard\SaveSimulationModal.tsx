import React, { useState } from 'react';
import { X, Save, AlertCircle } from 'lucide-react';
import { SaveSimulationData } from '../../types';

interface SaveSimulationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: { nom: string; description?: string }) => Promise<void>;
  simulationData: SaveSimulationData;
  loading?: boolean;
}

export function SaveSimulationModal({ 
  isOpen, 
  onClose, 
  onSave, 
  simulationData, 
  loading = false 
}: SaveSimulationModalProps) {
  const [nom, setNom] = useState('');
  const [description, setDescription] = useState('');
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!nom.trim()) {
      setError('Le nom de la simulation est requis');
      return;
    }

    try {
      await onSave({ nom: nom.trim(), description: description.trim() || undefined });
      setNom('');
      setDescription('');
      onClose();
    } catch (err) {
      setError('Erreur lors de la sauvegarde de la simulation');
      console.error('Erreur sauvegarde:', err);
    }
  };

  const handleClose = () => {
    setNom('');
    setDescription('');
    setError(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="flex items-center justify-between p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">
            Sauvegarder la simulation
          </h3>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={loading}
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            {/* Informations sur la simulation */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-800 mb-2">Simulation à sauvegarder</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div>• Commune: {simulationData.commune_data.nom}</div>
                <div>• Canadair nécessaires: {Math.ceil(simulationData.results.canadairsExact)}</div>
                <div>• Dash nécessaires: {Math.ceil(simulationData.results.dashsExact)}</div>
              </div>
            </div>

            {/* Nom de la simulation */}
            <div>
              <label htmlFor="nom" className="block text-sm font-medium text-gray-700 mb-1">
                Nom de la simulation *
              </label>
              <input
                type="text"
                id="nom"
                value={nom}
                onChange={(e) => setNom(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                placeholder="Ex: Intervention Nîmes - Juillet 2024"
                disabled={loading}
                required
              />
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Description (optionnelle)
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                placeholder="Ajoutez des notes sur cette simulation..."
                disabled={loading}
              />
            </div>

            {/* Erreur */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <span className="text-sm text-red-700">{error}</span>
              </div>
            )}
          </div>

          {/* Boutons */}
          <div className="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              disabled={loading}
            >
              Annuler
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={loading || !nom.trim()}
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Sauvegarde...</span>
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  <span>Sauvegarder</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
