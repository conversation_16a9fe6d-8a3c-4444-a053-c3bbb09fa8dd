# Import des plans d'eau depuis CSV

Ce script permet d'importer des plans d'eau depuis un fichier CSV dans la base de données Supabase.

## ⚠️ Correction de l'erreur "invalid input syntax for type numeric"

Cette erreur se produit quand le CSV contient des coordonnées vides (`""` ou cellules vides). Le script a été mis à jour pour gérer automatiquement ces cas.

## Format CSV attendu

Le fichier CSV doit avoir les colonnes suivantes :
```csv
Comm<PERSON>,<PERSON><PERSON><PERSON>,<PERSON>t,Long
LES MUREAUX,Non Ecopage,49.001358940383795,1.9127045567936576
CHARTRETTES,Ecopage,48.48459227791252,2.679946486157216
LA ROCHETTE,Ecopage,,
LA ROCHELLE,Ecopage,46.151300437728985,-1.2451307727521674
```

### Colonnes :
- **Commune** : Nom de la commune (obligatoire)
- **Accès** : Type d'accès ("Ecopage" ou "Non Ecopage")
- **Lat** : Latitude (peut être vide - sera ignorée)
- **Long** : Longitude (peut être vide - sera ignorée)

### ✅ Gestion des cas problématiques :
- **Coordonnées vides** : Automatiquement ignorées
- **Coordonnées invalides** : Détectées et signalées
- **Caractères spéciaux** : Nettoyés automatiquement
- **Encodage** : Support UTF-8 avec BOM

## Configuration

1. **Modifiez le script** `scripts/importPlansEau.js` :
   - Remplacez `YOUR_SUPABASE_URL` par votre URL Supabase
   - Remplacez `YOUR_SUPABASE_SERVICE_ROLE_KEY` par votre clé service role

2. **Appliquez les migrations** de structure :
   ```bash
   # Dans l'éditeur SQL de Supabase, exécutez :
   # - supabase/migrations/20250702184830_heavy_snowflake.sql
   # - supabase/migrations/20250702185813_withered_thunder.sql
   ```

## Utilisation

### 1. Validation du fichier (recommandé)
```bash
node scripts/importPlansEau.js ./data/plans_eau.csv --validate
```
Cette commande vérifie votre fichier sans rien importer et vous montre les erreurs éventuelles.

### 2. Import simple
```bash
node scripts/importPlansEau.js ./data/plans_eau.csv
```

### 3. Import avec nettoyage préalable
```bash
node scripts/importPlansEau.js ./data/plans_eau.csv --clear
```

## Fonctionnalités de correction

### ✅ Gestion robuste des coordonnées :
- **Valeurs vides** : `""`, `null`, cellules vides → ignorées
- **Valeurs invalides** : Texte, caractères spéciaux → ignorées  
- **Plages invalides** : Latitude hors [-90,90], longitude hors [-180,180] → ignorées
- **Formats divers** : Espaces, guillemets → nettoyés automatiquement

### ✅ Validation automatique :
- Vérification des en-têtes requis
- Validation des coordonnées GPS
- Détection des doublons potentiels
- Rapport détaillé des erreurs

### ✅ Import par lots :
- Traitement par groupes de 50 pour éviter les timeouts
- Gestion des erreurs par lot
- Progression affichée en temps réel

## Exemple de sortie

```
🚀 Début de l'import des plans d'eau...

✅ Fichier CSV lu avec succès
📊 4 lignes trouvées dans le CSV
📋 En-têtes détectés: Commune, Accès, Lat, Long
🔄 2 plans d'eau valides à importer
⚠️ 2 entrées invalides ignorées:
  • Ligne 4: Coordonnées invalides (Lat: , Long: )

📋 Aperçu des données à importer:
1. Plan d'eau LES MUREAUX (Non écopage)
   Commune: LES MUREAUX
   Coordonnées: 49.0013589, 1.9127046
   Type: lac, Écopage: Non

📤 Import du lot 1/1 (2 éléments)...
✅ Lot 1 importé avec succès

🎉 2 plans d'eau importés avec succès !

📈 Statistiques finales:
• Plans d'eau avec écopage: 1
• Plans d'eau sans écopage: 1
• Répartition par type:
  - lac: 2
• Total importé: 2
• Entrées ignorées: 2
```

## Structure de la table après import

```sql
CREATE TABLE plans_eau (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  nom text NOT NULL,
  commune text,                    -- Nom de la commune
  latitude numeric(10,7),          -- Précision optimisée
  longitude numeric(11,7),         -- Précision optimisée  
  type text NOT NULL CHECK (type IN ('mer', 'lac', 'etang', 'riviere')),
  accessible boolean DEFAULT true,
  ecopage boolean DEFAULT true,    -- Possibilité d'écopage
  created_at timestamptz DEFAULT now()
);
```

## Dépannage

### ❌ "invalid input syntax for type numeric"
**Solution** : Utilisez la version mise à jour du script qui gère automatiquement les coordonnées vides.

### ❌ "En-têtes manquants"
**Solution** : Vérifiez que votre CSV a bien les colonnes `Commune`, `Accès`, `Lat`, `Long`.

### ❌ "Aucune donnée trouvée"
**Solution** : Vérifiez l'encodage de votre fichier (UTF-8 recommandé).

### ❌ "Missing Supabase environment variables"
**Solution** : Configurez vos clés Supabase dans le script.

## Sécurité

⚠️ **Important :** 
- Utilisez la clé service_role uniquement pour ce script
- Ne commitez jamais vos clés dans le code source
- Testez d'abord avec `--validate`
- Sauvegardez vos données avant `--clear`

## Prochaines étapes

Après un import réussi :
1. Vérifiez les données dans Supabase
2. Testez la recherche de communes dans l'application
3. Vérifiez que les calculs de distance fonctionnent
4. Ajustez les types de plans d'eau si nécessaire